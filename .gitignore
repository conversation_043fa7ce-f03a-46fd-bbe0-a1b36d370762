# 编译产物
*.exe
*.exe~
*.dll
*.so
*.dylib
*.out

# 中间文件
*.o
*.obj

# Go 模块缓存
go.sum
go.mod.sum

# 依赖下载目录（Go 1.11+ 模块模式下通常不需要，但旧版本可能需要）
vendor/

# 测试相关
*.test
*.prof

# 覆盖率报告
coverage.out
coverage.html

# 操作系统文件
.DS_Store        # macOS
Thumbs.db        # Windows

# IDE 配置（根据你使用的工具添加）
.idea/           # IntelliJ 系列
.vscode/         # VS Code
*.sublime-*      # Sublime Text
.project         # Eclipse
.classpath       # Eclipse

# 环境变量和配置文件
.env
.env.local
*.conf.local

# 日志文件
*.log