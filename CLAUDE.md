# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build and Run
- **Build binary**: `make build` or `go build -o bin/neoapi .`
- **Run development server**: `make run` or `go run .` (starts on port 8080)
- **Install dependencies**: `make deps` or `go mod download && go mod tidy`

### Testing
- **Run Go tests**: `make test` or `go test -v ./...`
- **Comprehensive API testing**: `./test_neoapi_comprehensive.sh` (includes all test scenarios)
- **Basic tests only**: `./test_neoapi_comprehensive.sh -b`
- **Verbose testing**: `./test_neoapi_comprehensive.sh -v`

### Development Tools
- **Generate Swagger docs**: `make swagger` or `swag init -g main.go`
- **Clean build artifacts**: `make clean`
- **Docker build**: `make docker-build`
- **Docker run**: `make docker-run`

### Linting and Code Quality
- **Format code**: `go fmt ./...`
- **Vet code**: `go vet ./...`

## Project Architecture

### High-Level Overview
NeoAPI is a Go-based REST API service that provides unified data querying and analysis for Yangtze River shipping data using Neo4j graph database. The service implements intelligent fallback strategies to maximize data value even when specific metrics are unavailable.

### Core Architecture Layers

```
┌─────────────────┐
│   HTTP Clients  │
└─────────┬───────┘
          │
┌─────────▼───────┐
│   Gin Router    │ (/api/* routes)
└─────────┬───────┘
          │
┌─────────▼───────┐
│    Handlers     │ (unified_handler.go, metric_handler.go)
└─────────┬───────┘
          │
┌─────────▼───────┐
│    Services     │ (query_service.go, entity_*_service.go)
└─────────┬───────┘
          │
┌─────────▼───────┐
│   Neo4j Driver  │ (Graph Database)
└─────────────────┘
```

### Directory Structure Significance

- **`internal/handlers/`**: HTTP request handlers implementing REST API endpoints
- **`internal/services/`**: Business logic layer containing query execution, entity resolution, and data processing
- **`internal/models/`**: Comprehensive data models defining entities, requests, and responses
- **`internal/time/`**: C-STEL time expression parser for flexible time queries
- **`internal/router/`**: Route definitions and middleware setup
- **`internal/utils/`**: Utility functions for query processing

### Key Architectural Patterns

#### Unified Query Interface
- Single endpoint `/api/v3/query` handles all query types: POINT, PROFILE, TREND, COMPARE, RANK, COMPOSE
- Request/response models in `models.go` support all query variations
- Query routing logic in `query_service.go` delegates to specialized implementations

#### Entity Resolution System
- Supports exact, fuzzy, and multi-entity matching strategies
- Entity types: Ship, Port, Province, Basin, ShippingRoute, CargoType
- Resolution logic handles MMSI, ship names, port names with intelligent fallbacks

#### Intelligent Fallback Strategy
- When specific metrics unavailable, returns complete entity profile data
- Fallback service provides alternative information to maximize query value
- Status codes indicate fallback scenarios (e.g., "metric_unavailable_full_profile_returned")

#### Time Expression System (C-STEL)
- Supports flexible time expressions: R6M (recent 6 months), Y2024 (year 2024), M202406 (specific month)
- Time ranges: M202401_M202406 (January to June 2024)
- Quarters: Q2024Q1 (Q1 2024)
- Parser in `internal/time/cstel.go`

### Data Layer Architecture

#### Neo4j Graph Model
- **Core Entities**: Ship, Port, ShippingRoute, Province, Basin nodes
- **Time Nodes**: Year, YearMonth for temporal relationships
- **Statistics Nodes**: ShipMonthStat, PortMonthStat, RouteMonthStat
- **Cargo Classification**: CargoType, CargoCategory, ShipType relationships
- **Relationships**: STAT_FOR_SHIP, STAT_FOR_PORT, BELONGS_TO_PROVINCE, etc.

#### Service Layer Organization
- **QueryService**: Main orchestrator for unified queries
- **EntitySearchService**: Entity discovery and search functionality  
- **EntityQueryService**: Advanced entity querying with filters
- **RouteService**: Route-specific queries and analysis
- **MetricService**: Metric definitions and validation
- **ShipProfileService**: Comprehensive ship profile analysis

### Critical Implementation Details

#### Error Handling Strategy
- Structured error responses with error codes (ENTITY_NOT_FOUND, INVALID_REQUEST, QUERY_FAILED)
- Graceful degradation when data unavailable
- Detailed logging for debugging query execution

#### Configuration Management
- YAML configuration in `config.yaml`
- Environment variable support for database credentials
- Server and database configuration separation

#### Response Format Standardization
All API responses follow unified structure:
```json
{
  "status": "success|error|partial",
  "message": "Response message",
  "data": { /* specific data */ },
  "metadata": {
    "query_time": "timestamp",
    "execution_time_ms": 150,
    "api_version": "3.1"
  }
}
```

### Testing Architecture
- Comprehensive test script covers all API endpoints and query types
- Test scenarios include basic functionality, profile queries, comparison analysis, and error handling
- Parameterized testing with configurable entity names and API base URL
- Supports different test modes (basic, verbose, quick)

## Database Connection

The application connects to Neo4j using configuration from `config.yaml`:
- Default connection: `bolt://localhost:7687`
- Authentication: neo4j/neo4j123 (configurable)
- Connection pooling managed by Neo4j Go driver

## API Endpoints Structure

### Primary Query Endpoint
- `POST /api/v3/query` - Unified query interface for all query types

### Search and Discovery
- `GET /api/v3/search/entities` - Entity search with query parameters
- `POST /api/v3/search/entities` - Entity search with JSON body

### Specialized Endpoints
- `GET /api/health` - Health check
- `GET /api/v3/metadata/*` - Metadata endpoints for entities, metrics, time ranges
- Ship profile endpoints with various options for detailed analysis

## Important Development Notes

### Entity Identifier Handling
- Ships: Support both MMSI numbers and ship names
- Ports: Use port names with fuzzy matching capability
- Routes: RouteID or route name combinations
- Always implement resolution strategies (exact/fuzzy/multi)

### Time Expression Parsing
- Use C-STEL parser for all time-related queries
- Support relative expressions (R6M, R1Y) and absolute ranges
- Handle month, quarter, and yearly aggregations

### Query Implementation Pattern
1. Parse and validate request
2. Resolve entity identifiers
3. Parse time expressions
4. Execute Neo4j queries with proper error handling
5. Apply fallback strategies if needed
6. Format response with metadata

### Performance Considerations
- Use parameterized Neo4j queries to prevent injection
- Implement query timeouts and connection pooling
- Consider caching for frequently accessed metadata
- Log query execution times for performance monitoring