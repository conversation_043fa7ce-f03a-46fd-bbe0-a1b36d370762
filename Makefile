.PHONY: build run test clean deps swagger

# Build the binary
build:
	go build -o bin/neoapi .

# Run the server
run:
	go run .

# Install dependencies
deps:
	go mod download
	go mod tidy

# Run tests
test:
	go test -v ./...

# Clean build artifacts
clean:
	rm -rf bin/

# Generate swagger docs
swagger:
	swag init -g main.go

# Docker build
docker-build:
	docker build -t neoapi .

# Docker run
docker-run:
	docker run -p 8080:8080 --network host neoap<PERSON>

# Help
help:
	@echo "Available commands:"
	@echo "  build       - Build the binary"
	@echo "  run         - Run the server"
	@echo "  deps        - Install dependencies"
	@echo "  test        - Run tests"
	@echo "  clean       - Clean build artifacts"
	@echo "  swagger     - Generate swagger docs"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run  - Run Docker container"