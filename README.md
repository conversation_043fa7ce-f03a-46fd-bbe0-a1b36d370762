# NeoAPI - 长江航运智能分析API

[![Go Version](https://img.shields.io/badge/Go-1.19+-blue.svg)](https://golang.org)
[![Neo4j](https://img.shields.io/badge/Neo4j-5.x-green.svg)](https://neo4j.com)
[![API Version](https://img.shields.io/badge/API-v3.1-orange.svg)](docs/新API设计文档v3.1.md)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 🎯 项目概述

NeoAPI 是一个基于 Neo4j 图数据库的统一数据查询和分析API，专门用于长江航运数据的查询、比较和分析。项目采用了智能降级策略，确保在数据不完整的情况下仍能为用户提供最大化的信息价值。

### 核心特性

- **🔍 统一查询架构** - 支持 POINT、PROFILE、TREND、COMPARE、RANK、COMPOSE 等多种查询类型
- **🚢 多实体支持** - 船舶(Ship)、港口(Port)、航线(ShippingRoute)、省份(Province)、流域(Basin)
- **⏰ 灵活时间表达式** - 基于 C-STEL 标准，支持月度、季度、年度、相对时间等多种格式
- **🧠 智能降级策略** - 当特定指标不可用时，自动返回实体的完整属性信息
- **📊 画像分析** - 提供船舶画像、港口画像等详细分析功能
- **🔄 实时查询** - 支持实时数据查询和历史数据分析

## 🏗️ 技术架构

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Apps   │    │   Web Frontend  │    │   Mobile Apps   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │       NeoAPI Server       │
                    │     (Go + Gin Framework)  │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      Neo4j Database       │
                    │    (Graph Database)       │
                    └───────────────────────────┘
```

### 代码结构
```
neoapi/
├── main.go                     # 应用入口
├── config.yaml                 # 配置文件
├── internal/                   # 内部包
│   ├── handlers/               # HTTP 处理器
│   │   ├── unified_handler.go  # 统一查询处理器
│   │   └── metric_handler.go   # 指标处理器
│   ├── services/               # 业务逻辑层
│   │   ├── query_service.go    # 核心查询服务
│   │   ├── query_implementations.go # 查询实现
│   │   ├── fallback_service.go # 降级处理服务
│   │   ├── ship_profile_service.go # 船舶画像服务
│   │   └── entity_search_service.go # 实体搜索服务
│   ├── models/                 # 数据模型
│   │   └── models.go           # 统一数据模型
│   ├── router/                 # 路由配置
│   │   └── router.go           # 路由设置
│   ├── middleware/             # 中间件
│   │   └── middleware.go       # 通用中间件
│   ├── time/                   # 时间解析器
│   │   └── cstel.go            # C-STEL 时间表达式解析
│   └── utils/                  # 工具函数
│       └── query_utils.go      # 查询工具函数
├── docs/                       # 文档目录
│   ├── 新API设计文档v3.1.md    # API 设计文档
│   ├── API_v3.1_使用指南.md    # API 使用指南
│   ├── 数据库设计.md           # 数据库设计文档
│   └── 时间设计文档.md         # 时间表达式设计
├── design/                     # 设计文档
│   ├── PROJECT_SUMMARY.md      # 项目总结
│   ├── API_UNIFIED_ARCHITECTURE.md # 统一架构设计
│   └── EMPTY_VALUES_HANDLING_DESIGN.md # 空值处理设计
└── test_neoapi_comprehensive.sh # 综合测试脚本
```

## 🚀 快速开始

### 环境要求

- **Go**: 1.19 或更高版本
- **Neo4j**: 5.x 版本
- **操作系统**: Linux, macOS, Windows

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd neoapi
```

2. **安装依赖**
```bash
go mod download
```

3. **配置数据库**
```yaml
# config.yaml
server:
  port: "8080"

database:
  uri: "bolt://localhost:7687"
  username: "neo4j"
  password: "neo4j123"
```

4. **启动服务**
```bash
# 开发环境
go run main.go

# 生产环境
go build -o neoapi
./neoapi
```

5. **验证安装**
```bash
curl http://localhost:8080/api/health
```

### Docker 部署

```bash
# 构建镜像
docker build -t neoapi .

# 运行容器
docker run -p 8080:8080 \
  -e DATABASE_URI=bolt://neo4j:7687 \
  -e DATABASE_USERNAME=neo4j \
  -e DATABASE_PASSWORD=your_password \
  neoapi
```

## 📖 API 使用指南

### 基础接口

#### 健康检查
```bash
GET /api/health
```

#### 统一查询接口
```bash
POST /api/v3/query
Content-Type: application/json

{
  "query_type": "PROFILE",
  "entity": {
    "type": "Ship",
    "identifier": "汉海5号",
    "resolution_strategy": "fuzzy"
  },
  "metric": "船舶画像",
  "time_expression": "R6M",
  "options": {
    "detail_level": "full",
    "include_history": true
  }
}
```

### 查询类型

| 类型 | 说明 | 示例场景 |
|------|------|----------|
| **POINT** | 单点查询 | 获取特定时间点的货运量 |
| **PROFILE** | 画像查询 | 获取船舶的详细运营信息 |
| **TREND** | 趋势分析 | 分析港口吞吐量的月度变化 |
| **COMPARE** | 对比分析 | 比较两个港口的运营指标 |
| **RANK** | 排名分析 | 获取区域内港口的排名 |
| **COMPOSE** | 构成分析 | 分析货物类型的构成比例 |

### 时间表达式

| 表达式 | 说明 | 示例 |
|--------|------|------|
| `R6M` | 最近6个月 | 2024年1月-6月 |
| `Y2024` | 2024年全年 | 2024年1月-12月 |
| `M202406` | 2024年6月 | 2024年6月 |
| `Q2024Q1` | 2024年第1季度 | 2024年1-3月 |
| `M202401_M202406` | 时间范围 | 2024年1月到6月 |

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
./test_neoapi_comprehensive.sh

# 详细输出模式
./test_neoapi_comprehensive.sh -v

# 仅运行基础测试
./test_neoapi_comprehensive.sh -b

# 快速测试模式
./test_neoapi_comprehensive.sh -q
```

### 测试覆盖

- ✅ **基础功能**: API健康检查、元数据查询、搜索接口
- ✅ **画像接口**: 船舶画像、港口画像、多种参数组合
- ✅ **比较查询**: 实体对比、多实体比较、空值处理
- ✅ **高级分析**: 排名查询、基准比较、关联分析
- ✅ **错误处理**: 参数验证、类型检查、异常情况

## 🔧 配置说明

### 服务器配置
```yaml
server:
  port: "8080"              # 服务端口
  read_timeout: "30s"       # 读取超时
  write_timeout: "30s"      # 写入超时
```

### 数据库配置
```yaml
database:
  uri: "bolt://localhost:7687"  # Neo4j 连接地址
  username: "neo4j"             # 用户名
  password: "neo4j123"          # 密码
  max_connections: 100          # 最大连接数
  connection_timeout: "30s"     # 连接超时
```

## 📊 核心功能

### 智能降级策略

当请求的指标不可用时，系统会自动返回实体的完整属性信息：

```json
{
  "entity_name": "重庆港",
  "requested_metric": "不存在的指标",
  "status": "metric_unavailable_full_profile_returned",
  "profile_data": {
    "available_metrics": {
      "进港艘次": {"value": 1234, "unit": "艘"},
      "出港艘次": {"value": 1156, "unit": "艘"},
      "进港货量": {"value": 98765, "unit": "万吨"}
    }
  }
}
```

### 船舶画像分析

提供全面的船舶运营分析：
- 基本信息（船名、MMSI、船东等）
- 运营统计（货运量、航次、载重率等）
- 历史趋势（月度、季度变化）
- 航线分析（主要航线、港口分布）

### 港口画像分析

提供详细的港口运营分析：
- 吞吐量统计（进出港货量、船舶数量）
- 货物构成（货物类型分布）
- 船舶类型分析（船型分布、平均载重）
- 时间趋势（月度、季度变化）

## 💡 使用示例

### 1. 船舶画像查询

```bash
curl -X POST "http://localhost:8080/api/v3/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query_type": "PROFILE",
    "entity": {
      "type": "Ship",
      "identifier": "汉海5号",
      "resolution_strategy": "fuzzy"
    },
    "metric": "船舶画像",
    "time_expression": "R6M",
    "options": {
      "detail_level": "full",
      "include_history": true,
      "include_cargo_breakdown": true,
      "include_ship_analysis": true
    }
  }'
```

### 2. 港口比较分析

```bash
curl -X POST "http://localhost:8080/api/v3/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query_type": "COMPARE",
    "entities": [
      {"type": "Port", "identifier": "重庆港"},
      {"type": "Port", "identifier": "宜昌港"}
    ],
    "metric": "总吞吐量",
    "time_expression": "R6M"
  }'
```

### 3. 实体搜索

```bash
curl "http://localhost:8080/api/v3/search/entities?q=汉海&type=Ship&limit=10"
```

### 4. 趋势分析

```bash
curl -X POST "http://localhost:8080/api/v3/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query_type": "TREND",
    "entity": {
      "type": "Port",
      "identifier": "重庆港"
    },
    "metric": "月度吞吐量",
    "time_expression": "Y2024"
  }'
```

## 🗄️ 数据库设计

### Neo4j 图数据库模型

项目使用 Neo4j 图数据库存储长江航运数据，主要节点类型包括：

#### 核心实体节点
- **Ship** - 船舶档案信息
- **Port** - 港口基础信息
- **ShippingRoute** - 航线信息
- **Province** - 省份信息
- **Basin** - 流域信息

#### 分类节点
- **ShipType** - 船舶类型
- **CargoType** - 货物类型
- **Year/YearMonth** - 时间节点

#### 统计节点
- **ShipMonthStat** - 船舶月度统计
- **PortMonthStat** - 港口月度统计
- **RouteMonthStat** - 航线月度统计

### 关系设计

```cypher
// 船舶与统计数据的关系
(Ship)-[:STAT_FOR_SHIP]->(ShipMonthStat)-[:STAT_FOR_MONTH]->(YearMonth)

// 港口与统计数据的关系
(Port)-[:STAT_FOR_PORT]->(PortMonthStat)-[:STAT_FOR_MONTH]->(YearMonth)

// 货物类型关系
(ShipMonthStat)-[:CARGO_STAT_FOR_TYPE]->(CargoType)

// 地理层级关系
(Port)-[:BELONGS_TO_PROVINCE]->(Province)
(Province)-[:BELONGS_TO_BASIN]->(Basin)
```

## 🔍 API 详细说明

### 响应格式

所有API响应都遵循统一的格式：

```json
{
  "status": "success|error|partial",
  "message": "响应消息",
  "data": {
    // 具体数据内容
  },
  "metadata": {
    "query_time": "2024-01-15T10:30:00Z",
    "execution_time_ms": 150,
    "data_source": "neo4j",
    "api_version": "3.1"
  }
}
```

### 错误处理

```json
{
  "status": "error",
  "error_code": "ENTITY_NOT_FOUND",
  "message": "指定的实体不存在",
  "details": {
    "entity_type": "Ship",
    "identifier": "不存在的船舶",
    "suggestions": ["汉海5号", "汉海6号"]
  }
}
```

### 状态码说明

| 状态 | 说明 | 场景 |
|------|------|------|
| `success` | 查询成功 | 正常返回数据 |
| `partial` | 部分成功 | 降级处理返回 |
| `error` | 查询失败 | 参数错误或系统异常 |
| `metric_unavailable_full_profile_returned` | 指标不可用，返回完整画像 | 智能降级 |

## 🚀 部署指南

### 生产环境部署

#### 1. 使用 Docker Compose

```yaml
# docker-compose.yml
version: '3.8'
services:
  neoapi:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URI=bolt://neo4j:7687
      - DATABASE_USERNAME=neo4j
      - DATABASE_PASSWORD=${NEO4J_PASSWORD}
    depends_on:
      - neo4j
    restart: unless-stopped

  neo4j:
    image: neo4j:5.13
    ports:
      - "7474:7474"
      - "7687:7687"
    environment:
      - NEO4J_AUTH=neo4j/${NEO4J_PASSWORD}
      - NEO4J_PLUGINS=["apoc"]
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
    restart: unless-stopped

volumes:
  neo4j_data:
  neo4j_logs:
```

#### 2. Kubernetes 部署

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: neoapi
spec:
  replicas: 3
  selector:
    matchLabels:
      app: neoapi
  template:
    metadata:
      labels:
        app: neoapi
    spec:
      containers:
      - name: neoapi
        image: neoapi:latest
        ports:
        - containerPort: 8080
        env:
        - name: DATABASE_URI
          value: "bolt://neo4j-service:7687"
        - name: DATABASE_USERNAME
          valueFrom:
            secretKeyRef:
              name: neo4j-secret
              key: username
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: neo4j-secret
              key: password
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

### 性能优化

#### 1. Neo4j 优化配置

```properties
# neo4j.conf
dbms.memory.heap.initial_size=2G
dbms.memory.heap.max_size=4G
dbms.memory.pagecache.size=2G
dbms.default_database=neo4j
dbms.security.auth_enabled=true
```

#### 2. 应用层优化

- **连接池配置**: 设置合适的数据库连接池大小
- **查询缓存**: 对频繁查询的结果进行缓存
- **异步处理**: 对耗时操作使用异步处理
- **分页查询**: 大数据量查询使用分页机制

## 📈 监控和日志

### 健康检查端点

```bash
# 基础健康检查
GET /api/health

# 详细健康检查
GET /api/health/detailed
```

### 指标监控

项目支持 Prometheus 指标监控：

```bash
# 指标端点
GET /metrics
```

主要监控指标：
- `neoapi_requests_total` - 请求总数
- `neoapi_request_duration_seconds` - 请求耗时
- `neoapi_database_connections_active` - 活跃数据库连接数
- `neoapi_query_errors_total` - 查询错误总数

### 日志配置

```yaml
# config.yaml
logging:
  level: "info"           # debug, info, warn, error
  format: "json"          # json, text
  output: "stdout"        # stdout, file
  file_path: "/var/log/neoapi.log"
```

## 🔧 故障排除

### 常见问题

#### 1. 数据库连接失败

```bash
# 检查 Neo4j 服务状态
docker ps | grep neo4j

# 检查网络连接
telnet localhost 7687

# 查看 Neo4j 日志
docker logs neo4j-container
```

#### 2. 查询性能问题

```cypher
// 检查索引
SHOW INDEXES

// 创建必要索引
CREATE INDEX ship_name_index FOR (s:Ship) ON (s.name)
CREATE INDEX port_name_index FOR (p:Port) ON (p.name)
```

#### 3. 内存使用过高

```bash
# 监控内存使用
docker stats neoapi-container

# 调整 JVM 堆内存
export NEO4J_HEAP_SIZE=4G
```

### 调试模式

```bash
# 启用调试日志
export LOG_LEVEL=debug
go run main.go

# 查看详细查询日志
curl "http://localhost:8080/api/v3/query?debug=true"
```

## 🤝 贡献指南

### 开发环境设置

1. **Fork 项目**
```bash
git clone https://github.com/yourusername/neoapi.git
cd neoapi
```

2. **安装开发依赖**
```bash
go mod download
go install github.com/swaggo/swag/cmd/swag@latest
```

3. **运行测试**
```bash
go test ./...
./test_neoapi_comprehensive.sh
```

4. **代码格式化**
```bash
go fmt ./...
go vet ./...
```

### 提交规范

- 使用语义化提交信息
- 添加适当的测试用例
- 更新相关文档
- 确保所有测试通过

### Pull Request 流程

1. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
2. 提交更改 (`git commit -m 'feat: Add some AmazingFeature'`)
3. 推送到分支 (`git push origin feature/AmazingFeature`)
4. 开启 Pull Request
5. 等待代码审查

## 📚 相关文档

### 设计文档
- [API 设计文档 v3.1](docs/新API设计文档v3.1.md) - 详细的API设计说明
- [数据库设计文档](docs/数据库设计.md) - Neo4j数据库模型设计
- [时间表达式设计](docs/时间设计文档.md) - C-STEL时间表达式规范
- [项目架构设计](design/API_UNIFIED_ARCHITECTURE.md) - 统一架构设计
- [空值处理设计](design/EMPTY_VALUES_HANDLING_DESIGN.md) - 智能降级策略设计

### 使用指南
- [API 使用指南](docs/API_v3.1_使用指南.md) - 详细的API使用说明
- [项目总结](design/PROJECT_SUMMARY.md) - 项目功能和特性总结

## 🏆 项目亮点

### 技术创新
- **智能降级策略**: 当指标不可用时自动返回完整实体信息
- **统一查询接口**: 单一入口支持多种查询类型
- **C-STEL时间表达式**: 灵活的时间表达式支持
- **图数据库优化**: 基于Neo4j的高效查询设计

### 业务价值
- **提升查询成功率**: 通过智能降级减少查询失败
- **简化API使用**: 统一接口降低学习成本
- **丰富数据分析**: 支持多维度数据分析和比较
- **实时响应**: 快速的查询响应和实时数据支持

### 工程质量
- **完整测试覆盖**: 自动化测试脚本覆盖所有核心功能
- **详细文档**: 完善的设计文档和使用指南
- **容器化部署**: 支持Docker和Kubernetes部署
- **监控告警**: 完整的监控和日志系统

## 🔮 发展路线图

### v3.2 (2024 Q4)
- [ ] **性能优化**
  - [ ] 查询结果缓存机制
  - [ ] 数据库连接池优化
  - [ ] 异步查询处理
- [ ] **功能增强**
  - [ ] 更多实体类型支持 (货物类型、船舶类型)
  - [ ] 高级过滤器功能
  - [ ] 批量查询接口
- [ ] **监控改进**
  - [ ] Prometheus 指标集成
  - [ ] 分布式链路追踪
  - [ ] 性能基准测试

### v4.0 (2025 Q2)
- [ ] **智能分析**
  - [ ] 机器学习驱动的预测分析
  - [ ] 异常检测和告警
  - [ ] 智能推荐系统
- [ ] **可视化界面**
  - [ ] Web管理界面
  - [ ] 数据可视化大屏
  - [ ] 交互式查询构建器
- [ ] **企业功能**
  - [ ] 多租户支持
  - [ ] 权限管理系统
  - [ ] API访问控制

### v5.0 (2025 Q4)
- [ ] **微服务架构**
  - [ ] 服务拆分和治理
  - [ ] 分布式缓存
  - [ ] 消息队列集成
- [ ] **数据湖集成**
  - [ ] 多数据源支持
  - [ ] 实时数据流处理
  - [ ] 数据血缘追踪

## 📊 性能基准

### 查询性能 (基于标准硬件配置)

| 查询类型 | 平均响应时间 | 95%分位数 | QPS |
|----------|-------------|-----------|-----|
| POINT | 50ms | 100ms | 1000 |
| PROFILE | 200ms | 500ms | 200 |
| TREND | 150ms | 300ms | 300 |
| COMPARE | 300ms | 800ms | 100 |
| RANK | 500ms | 1200ms | 50 |

### 系统资源

- **内存使用**: 512MB - 2GB (取决于查询复杂度)
- **CPU使用**: 2-4核心推荐
- **存储**: 10GB+ (取决于数据量)
- **网络**: 100Mbps+ 推荐

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

```
MIT License

Copyright (c) 2024 NeoAPI Project

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## 📞 联系方式

### 项目团队
- **项目负责人**: NeoAPI Development Team
- **技术支持**: <EMAIL>
- **商务合作**: <EMAIL>

### 社区
- **GitHub**: [https://github.com/neoapi/neoapi](https://github.com/neoapi/neoapi)
- **文档站点**: [https://docs.neoapi.com](https://docs.neoapi.com)
- **问题反馈**: [GitHub Issues](https://github.com/neoapi/neoapi/issues)
- **功能建议**: [GitHub Discussions](https://github.com/neoapi/neoapi/discussions)

### 技术交流
- **技术博客**: [https://blog.neoapi.com](https://blog.neoapi.com)
- **开发者社区**: [https://community.neoapi.com](https://community.neoapi.com)
- **微信群**: 扫描二维码加入技术交流群

## 🙏 致谢

感谢以下开源项目和社区的支持：

- [Neo4j](https://neo4j.com) - 强大的图数据库
- [Gin](https://gin-gonic.com) - 高性能的Go Web框架
- [Go](https://golang.org) - 优秀的编程语言
- [Docker](https://docker.com) - 容器化技术
- [Kubernetes](https://kubernetes.io) - 容器编排平台

特别感谢所有贡献者和用户的支持与反馈！

---

<div align="center">

**NeoAPI** - 让长江航运数据分析更智能、更高效！

[![Star History Chart](https://api.star-history.com/svg?repos=neoapi/neoapi&type=Date)](https://star-history.com/#neoapi/neoapi&Date)

</div>
