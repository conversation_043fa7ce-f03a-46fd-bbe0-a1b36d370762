{"test_suite": "长江航运核心业务场景测试", "description": "精选最有意义的业务分析场景，涵盖港口运营、航线优化、船队管理、货物分析等核心需求", "api_endpoint": "POST /api/query", "test_cases": [{"scenario": "港口运营决策", "test_name": "武汉港运营画像", "business_context": "港口管理者需要全面了解港口运营状况，包括吞吐量、船舶流量、货物构成等", "request": {"query_type": "PROFILE", "entity": {"type": "Port", "identifier": "武汉港", "resolution_strategy": "fuzzy"}, "time_expression": "R6M"}}, {"scenario": "港口运营决策", "test_name": "长江中游港口吞吐量对比", "business_context": "了解武汉港与竞争对手的市场地位，制定发展策略", "request": {"query_type": "COMPARE", "entity": {"type": "Port", "identifier": ["武汉港", "岳阳港", "九江港"], "resolution_strategy": "fuzzy"}, "metric": "", "time_expression": "M202407"}}, {"scenario": "港口运营决策", "test_name": "武汉港煤炭运输趋势", "business_context": "监控重点货种变化趋势，为港口设施配置和发展规划提供依据", "request": {"query_type": "TREND", "entity": {"type": "Port", "identifier": "武汉港"}, "metric": "进港货量", "time_expression": "R12M", "filters": {"cargo_type": "煤炭及制品"}}}, {"scenario": "航线运营优化", "test_name": "武汉出发主要航线对比", "business_context": "航运公司需要对比不同航线表现，优化运力配置和航线选择", "request": {"query_type": "COMPARE", "entity": {"type": "ShippingRoute", "identifier": ["武汉-南京航线", "武汉-上海航线"], "resolution_strategy": "fuzzy"}, "metric": "航线货运量", "time_expression": "M202407"}}, {"scenario": "航线运营优化", "test_name": "长江干线繁忙航线TOP5", "business_context": "识别最繁忙的航线，发现市场机会，考虑新增运力投入", "request": {"query_type": "RANK", "entity": {"type": "ShippingRoute", "scope": "长江干线"}, "metric": "航线货运量", "time_expression": "M202407", "limit": 5}}, {"scenario": "船队管理优化", "test_name": "汉海5号船舶画像", "business_context": "深度分析重点船舶运营情况，了解航线偏好、货物构成、效率指标", "request": {"query_type": "PROFILE", "entity": {"type": "Ship", "identifier": "汉海5号", "resolution_strategy": "fuzzy"}, "time_expression": "R6M"}}, {"scenario": "船队管理优化", "test_name": "散货船载货量TOP10", "business_context": "识别散货船队中表现最优的船舶，总结最佳运营实践", "request": {"query_type": "RANK", "entity": {"type": "Ship", "scope": "长江航运"}, "metric": "载货量", "time_expression": "M202407", "filters": {"ship_type": "散货船"}, "limit": 10}}, {"scenario": "货物运输分析", "test_name": "主要货物运输量对比", "business_context": "货主了解不同货物类型的运输规模，制定物流策略", "request": {"query_type": "COMPARE", "entity": {"type": "CargoType", "identifier": ["煤炭及制品", "金属矿石", "石油天然气及制品"]}, "metric": "运输量", "time_expression": "M202407"}}, {"scenario": "货物运输分析", "test_name": "煤炭运输年度趋势", "business_context": "分析煤炭运输的季节性规律，为库存和运力计划提供依据", "request": {"query_type": "TREND", "entity": {"type": "CargoType", "identifier": "煤炭及制品"}, "metric": "运输量", "time_expression": "R12M"}}, {"scenario": "多维度分析", "test_name": "武汉-南京航线煤炭运输", "business_context": "分析特定航线特定货物的运输情况，为专业化运输服务提供数据支持", "request": {"query_type": "POINT", "entity": {"type": "ShippingRoute", "identifier": "武汉-南京航线"}, "metric": "航线货运量", "time_expression": "M202407", "filters": {"cargo_type": "煤炭及制品"}}}, {"scenario": "多维度分析", "test_name": "散货船在武汉港表现", "business_context": "分析特定船型在重点港口的活跃程度，优化港口服务", "request": {"query_type": "POINT", "entity": {"type": "Port", "identifier": "武汉港"}, "metric": "进港艘次", "time_expression": "M202407", "filters": {"ship_type": "散货船"}}}, {"scenario": "多维度分析", "test_name": "武汉港货物构成", "business_context": "了解港口货物结构，为港口设施规划和货种结构优化提供依据", "request": {"query_type": "COMPOSE", "entity": {"type": "Port", "identifier": "武汉港"}, "metric": "货物构成", "time_expression": "M202407"}}], "usage_instructions": {"endpoint": "POST http://localhost:8080/api/query", "content_type": "application/json; charset=utf-8", "example_curl": "curl -X POST http://localhost:8080/api/query -H 'Content-Type: application/json; charset=utf-8' -d '{\"query_type\":\"PROFILE\",\"entity\":{\"type\":\"Port\",\"identifier\":\"武汉港\"}}'"}, "expected_results": {"success_criteria": "response.status === 'success'", "data_validation": "response.data should contain relevant business metrics", "performance": "response time < 2 seconds for most queries"}}