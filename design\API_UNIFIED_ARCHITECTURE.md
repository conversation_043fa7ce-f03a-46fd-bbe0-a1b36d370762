# NeoAPI 统一架构文档

## 概述

NeoAPI 采用统一架构设计，提供分层的API接口，支持海事数据分析的各种查询需求。所有接口都使用统一的 `/api/` 路径前缀，不再维护多个版本。

## 架构特点

- **统一路径**: 所有API使用 `/api/` 前缀，无版本号
- **分层设计**: 按功能分组，包括核心查询、分析查询、搜索、实时数据、元数据等
- **向后兼容**: 支持GET和POST两种方法的搜索接口
- **统一响应**: 所有接口使用一致的响应格式

## API接口清单

### 1. 核心查询接口

**路径**: `POST /api/core/query`

支持高频查询类型：POINT、PROFILE、TREND

```json
{
  "query_type": "POINT",
  "entity": {
    "type": "Ship",
    "identifier": "汉海5号",
    "resolution_strategy": "fuzzy"
  },
  "metric": "有效营运率",
  "time_expression": "M202407"
}
```

### 2. 分析查询接口

**路径**: `POST /api/analytics/query`

支持分析型查询类型：COMPARE、RANK、COMPOSE

```json
{
  "query_type": "COMPARE",
  "entity": {
    "type": "Ship",
    "identifier": "汉海5号"
  },
  "metric": "货运量",
  "time_expression": "R6M"
}
```

### 3. 统一查询接口

**路径**: `POST /api/query`

支持所有查询类型，兼容原有功能

```json
{
  "query_type": "POINT",
  "entity": {
    "type": "Ship",
    "identifier": "汉海5号"
  },
  "metric": "有效营运率",
  "time_expression": "M202407"
}
```

### 4. 实体搜索接口

#### POST方法（推荐）
**路径**: `POST /api/search/entities`

```json
{
  "query": "汉海5号",
  "entity_type": "Ship",
  "strategy": "fuzzy",
  "limit": 10
}
```

#### GET方法（兼容性）
**路径**: `GET /api/search/entities?q=汉海5号&type=Ship&strategy=fuzzy&limit=10`

### 5. 实时数据接口

**路径**: `GET /api/realtime/{entity_type}/{entity_id}`

获取指定实体的实时状态数据

示例：`GET /api/realtime/Ship/汉海5号`

### 6. 元数据服务接口

**路径**: `GET /api/metadata/{type}`

支持的元数据类型：
- `entities`: 支持的实体类型
- `metrics`: 指标列表
- `cargo_types`: 货物类型
- `ship_types`: 船舶类型

示例：`GET /api/metadata/entities`

### 7. 实体画像接口

#### 船舶画像
**路径**: `GET /api/profiles/ships/{identifier}?time_expression=R6M`

#### 港口画像
**路径**: `GET /api/profiles/ports/{identifier}?time_expression=R6M`

#### 航线画像
**路径**: `GET /api/profiles/routes/{identifier}?time_expression=R6M`

### 8. 健康检查接口

**路径**: `GET /api/health`

返回API服务状态信息

## 查询类型说明

### 核心查询类型
- **POINT**: 单点查询，获取特定时间点的数据
- **PROFILE**: 画像查询，获取实体的综合信息
- **TREND**: 趋势查询，获取时间序列数据

### 分析查询类型
- **COMPARE**: 对比查询，比较多个实体或时间段
- **RANK**: 排名查询，获取排序结果
- **COMPOSE**: 组合查询，复杂的多维度分析

## 实体类型

- **Ship**: 船舶
- **Port**: 港口
- **Province**: 省份
- **Basin**: 流域
- **ShippingRoute**: 航线
- **CargoType**: 货物类型

## 时间表达式

- **绝对时间**: `M202407` (2024年7月)
- **相对时间**: `R6M` (最近6个月), `R1Y` (最近1年)
- **时间范围**: `M202401-M202406` (2024年1月到6月)

## 响应格式

所有接口使用统一的响应格式：

```json
{
  "query_meta": {
    "query_type": "POINT",
    "entity": {
      "type": "Ship",
      "identifier": "汉海5号"
    },
    "metric": "有效营运率",
    "time_expression": "M202407"
  },
  "data": {
    // 具体数据内容
  },
  "performance": {
    "query_time_ms": 150,
    "data_source": "neo4j",
    "cache_hit": false
  },
  "status": "success",
  "timestamp": "2024-07-15T10:30:00Z"
}
```

## 空值处理策略

### 智能降级机制

当比较查询中特定指标不可用时，NeoAPI采用智能降级策略，自动返回实体的完整属性集合，确保用户获得最大化的信息价值。

### 处理状态

- `success`: 特定指标查询成功
- `zero_value`: 特定指标值为零
- `metric_unavailable_full_profile_returned`: 特定指标不可用，已返回完整属性
- `error`: 完全无法获取数据

### 完整属性响应示例

```json
{
  "entity_name": "宜昌港",
  "requested_metric": "总吞吐量",
  "value": null,
  "status": "metric_unavailable_full_profile_returned",
  "message": "指标'总吞吐量'不可用，已返回完整属性",
  "profile_data": {
    "basic_info": {
      "name": "宜昌港",
      "type": "Port"
    },
    "available_metrics": {
      "进港艘次": {"value": 856, "unit": "艘"},
      "出港艘次": {"value": 743, "unit": "艘"},
      "进港货量": {"value": 45678, "unit": "万吨"}
    },
    "time_range": "R6M",
    "last_updated": "2024-07-15T10:30:00Z"
  }
}
```

### 统计信息增强

```json
{
  "statistics": {
    "total_entities": 3,
    "success_count": 1,
    "full_profile_count": 1,
    "error_count": 1,
    "specific_metric_availability_rate": 0.33,
    "data_availability_rate": 0.67
  }
}
```

## 错误处理

### 错误响应格式

```json
{
  "error": "Invalid query type for core endpoint",
  "message": "Core endpoint only supports POINT, PROFILE, and TREND queries",
  "code": "INVALID_QUERY_TYPE",
  "timestamp": "2024-07-15T10:30:00Z"
}
```

### 常见错误码

- `INVALID_REQUEST`: 请求格式错误
- `MISSING_PARAMETER`: 缺少必需参数
- `INVALID_QUERY_TYPE`: 查询类型无效
- `ENTITY_NOT_FOUND`: 实体不存在
- `SEARCH_FAILED`: 搜索失败
- `QUERY_FAILED`: 查询执行失败

## 使用示例

### 1. 获取船舶有效营运率

```bash
curl -X POST http://localhost:8080/api/core/query \
  -H "Content-Type: application/json" \
  -d '{
    "query_type": "POINT",
    "entity": {
      "type": "Ship",
      "identifier": "汉海5号"
    },
    "metric": "有效营运率",
    "time_expression": "M202407"
  }'
```

### 2. 搜索船舶

```bash
curl -X POST http://localhost:8080/api/search/entities \
  -H "Content-Type: application/json" \
  -d '{
    "query": "汉海5号",
    "entity_type": "Ship",
    "strategy": "fuzzy",
    "limit": 5
  }'
```

### 3. 获取船舶画像

```bash
curl "http://localhost:8080/api/profiles/ships/汉海5号?time_expression=R6M"
```

### 4. 获取元数据

```bash
curl "http://localhost:8080/api/metadata/entities"
```

## 测试

提供了完整的测试脚本：

```bash
# PowerShell (Windows)
powershell -ExecutionPolicy Bypass -File test_v4_api.ps1

# Bash (Linux/macOS)
chmod +x test_v4_api.sh
./test_v4_api.sh
```

## 迁移指南

### 从多版本API迁移

如果您之前使用的是版本化API（如 `/api/v3/` 或 `/api/v4.0/`），请按以下方式更新：

- `/api/v3/query` → `/api/query`
- `/api/v4.0/core/query` → `/api/core/query`
- `/api/v4.0/analytics/query` → `/api/analytics/query`
- `/api/v3/search` → `/api/search/entities`
- `/api/v3/ships/{id}/profile` → `/api/profiles/ships/{id}`
- `/api/v4.0/realtime/{type}/{id}` → `/api/realtime/{type}/{id}`
- `/api/v4.0/metadata/{type}` → `/api/metadata/{type}`

### 功能保持不变

- 所有查询参数和响应格式保持不变
- 查询类型验证规则保持不变
- 错误处理机制保持不变

## 总结

统一架构简化了API的使用，提供了清晰的功能分层，同时保持了向后兼容性。所有功能都可以通过统一的 `/api/` 路径访问，无需关心版本号的变化。
