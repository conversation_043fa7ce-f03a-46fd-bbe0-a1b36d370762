# 空值处理设计文档

## 概述

本文档详细说明了NeoAPI在处理多实体比较查询时，当特定指标为空或不可用时的处理策略。我们采用了"智能降级"的设计理念，即当请求的特定指标不可用时，自动返回该实体的完整属性集合，最大化用户获得的信息价值。

## 设计理念

### 核心原则
1. **信息不丢失**: 即使特定指标不可用，用户仍能获得有价值的信息
2. **智能降级**: 自动提供最佳可用信息，而非简单的错误响应
3. **透明性**: 清楚标识数据状态和来源
4. **一致性**: 保持API接口不变，丰富响应内容

### 处理策略
当特定指标查询失败时，系统会：
1. 尝试获取该实体的所有可用属性
2. 返回完整的属性集合而非空值
3. 明确标识这是"完整属性"而非"特定指标"
4. 提供详细的状态信息和说明

## 数据结构设计

### ComparisonDataPoint 扩展
```json
{
  "entity_name": "重庆港",
  "requested_metric": "总吞吐量",
  "value": null,
  "unit": "万吨",
  "status": "metric_unavailable_full_profile_returned",
  "message": "指标'总吞吐量'不可用，已返回完整属性",
  "profile_data": {
    "basic_info": {
      "name": "重庆港",
      "type": "Port"
    },
    "available_metrics": {
      "进港艘次": {
        "value": 1234,
        "unit": "艘"
      },
      "出港艘次": {
        "value": 1156,
        "unit": "艘"
      },
      "进港货量": {
        "value": 98765,
        "unit": "万吨"
      }
    },
    "time_range": "R6M",
    "last_updated": "2024-07-15T10:30:00Z",
    "data_sources": ["neo4j", "mongodb"]
  }
}
```

### 状态枚举
- `success`: 特定指标查询成功
- `zero_value`: 特定指标值为零
- `metric_unavailable_full_profile_returned`: 特定指标不可用，已返回完整属性
- `error`: 完全无法获取数据

## 实现逻辑

### 查询流程
```mermaid
graph TD
    A[开始查询特定指标] --> B{指标是否可用?}
    B -->|是| C[返回特定指标值]
    B -->|否| D[尝试获取完整属性]
    D --> E{完整属性是否可用?}
    E -->|是| F[返回完整属性集合]
    E -->|否| G[返回错误状态]
    C --> H[标记为success]
    F --> I[标记为metric_unavailable_full_profile_returned]
    G --> J[标记为error]
```

### 完整属性获取策略

#### 船舶实体
尝试获取以下指标：
- 有效营运率
- 载货量
- 航次数
- 载重率
- 周转量
- 货运量
- 平均载重
- 营运天数
- 停泊天数

#### 港口实体
尝试获取以下指标：
- 总吞吐量
- 进港艘次
- 出港艘次
- 进港货量
- 出港货量
- 集装箱吞吐量
- 散货吞吐量
- 液体货吞吐量
- 平均停泊时间

## 统计信息增强

### 新增统计指标
- `specific_metric_availability_rate`: 特定指标可用率
- `data_availability_rate`: 总体数据可用率（包含完整属性）
- `full_profile_count`: 返回完整属性的实体数量

### 统计计算逻辑
```json
{
  "statistics": {
    "total_entities": 5,
    "success_count": 2,
    "zero_value_count": 1,
    "full_profile_count": 1,
    "error_count": 1,
    "specific_metric_availability_rate": 0.6,
    "data_availability_rate": 0.8,
    "valid_values_count": 4,
    "average": 12345.67,
    "min_value": 0,
    "max_value": 98765
  }
}
```

## API响应示例

### 混合状态响应
```json
{
  "query_meta": {
    "query_type": "COMPARE",
    "entities": [
      {"type": "Port", "identifier": "重庆港"},
      {"type": "Port", "identifier": "不存在港口"},
      {"type": "Port", "identifier": "宜昌港"}
    ],
    "metric": "总吞吐量",
    "time_expression": "R6M"
  },
  "data": {
    "comparison": [
      {
        "entity_name": "重庆港",
        "requested_metric": "总吞吐量",
        "value": 12345.67,
        "unit": "万吨",
        "status": "success",
        "message": ""
      },
      {
        "entity_name": "不存在港口",
        "requested_metric": "总吞吐量",
        "value": null,
        "unit": "万吨",
        "status": "error",
        "message": "查询失败且无法获取完整属性: entity not found"
      },
      {
        "entity_name": "宜昌港",
        "requested_metric": "总吞吐量",
        "value": null,
        "unit": "万吨",
        "status": "metric_unavailable_full_profile_returned",
        "message": "指标'总吞吐量'不可用，已返回完整属性",
        "profile_data": {
          "basic_info": {
            "name": "宜昌港",
            "type": "Port"
          },
          "available_metrics": {
            "进港艘次": {"value": 856, "unit": "艘"},
            "出港艘次": {"value": 743, "unit": "艘"},
            "进港货量": {"value": 45678, "unit": "万吨"}
          },
          "time_range": "R6M",
          "last_updated": "2024-07-15T10:30:00Z"
        }
      }
    ],
    "statistics": {
      "total_entities": 3,
      "success_count": 1,
      "full_profile_count": 1,
      "error_count": 1,
      "specific_metric_availability_rate": 0.33,
      "data_availability_rate": 0.67
    }
  },
  "status": "success"
}
```

## 前端展示建议

### 用户界面设计
1. **特定指标可用**: 正常显示数值和图表
2. **指标不可用但有完整属性**: 
   - 显示"指标不可用"提示
   - 展示可用属性的摘要卡片
   - 提供"查看详细属性"链接
3. **完全无数据**: 显示"暂无数据"占位符

### 交互体验
- 鼠标悬停显示详细状态信息
- 点击可展开完整属性列表
- 提供数据来源和更新时间信息

## 性能考虑

### 优化策略
1. **缓存机制**: 缓存完整属性查询结果
2. **并行查询**: 同时查询特定指标和完整属性
3. **懒加载**: 仅在需要时获取完整属性
4. **批量优化**: 批量获取多个实体的完整属性

### 资源控制
- 限制完整属性的数量和深度
- 设置查询超时时间
- 实现降级策略的开关控制

## 向后兼容性

### API兼容性
- 保持现有API接口不变
- 新增字段为可选字段
- 现有客户端可正常工作

### 数据格式兼容
- 扩展现有数据结构
- 保持核心字段不变
- 新增字段使用omitempty标签

## 测试策略

### 测试用例覆盖
1. 特定指标可用的正常情况
2. 特定指标不可用但实体存在的情况
3. 实体完全不存在的情况
4. 混合状态的多实体比较
5. 大量实体的性能测试

### 验证要点
- 响应结构的正确性
- 状态标识的准确性
- 统计信息的计算正确性
- 完整属性的完整性

这种设计确保了即使在数据不完整的情况下，用户仍能获得最大化的信息价值，提升了API的实用性和用户体验。
