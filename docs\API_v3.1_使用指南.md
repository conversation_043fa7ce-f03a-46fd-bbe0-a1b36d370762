# NeoAPI v3.1 使用指南

## 概述

NeoAPI v3.1 是基于Neo4j图数据库的内河航运数据分析API，提供统一的查询接口，支持船舶画像、趋势分析、对比分析等多种查询类型。

## 主要特性

- **统一查询接口**：支持POINT、PROFILE、TREND、COMPARE、RANK、COMPOSE等查询类型
- **C-STEL时间表达式**：支持灵活的时间表达式解析
- **智能实体搜索**：支持模糊匹配和多标识符解析
- **船舶画像**：提供详细的船舶运营分析
- **航线维度**：支持航线相关的统计和分析
- **RESTful设计**：遵循REST API设计原则

## 基础信息

- **Base URL**: `http://localhost:8080`
- **API版本**: v3.1
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证

当前版本暂不需要认证，后续版本将支持API Key认证。

## 核心接口

### 1. 健康检查

检查API服务状态。

```http
GET /api/health
```

**响应示例：**
```json
{
  "status": "healthy",
  "version": "3.1.0",
  "message": "NeoAPI is running"
}
```

### 2. 统一查询接口

支持多种查询类型的统一接口。

```http
POST /api/v3/query
```

**请求体：**
```json
{
  "query_type": "PROFILE",
  "entity": {
    "type": "Ship",
    "identifier": "船舶名称或MMSI",
    "resolution_strategy": "fuzzy"
  },
  "metric": "船舶画像",
  "time_expression": "R6M",
  "options": {
    "detail_level": "full",
    "include_history": true,
    "include_cargo_breakdown": true,
    "include_ship_analysis": true
  }
}
```

**查询类型说明：**
- `POINT`: 单点查询，获取特定时间点的数据
- `PROFILE`: 画像查询，获取实体的详细信息
- `TREND`: 趋势分析，分析指标的变化趋势
- `COMPARE`: 对比分析，比较多个实体的指标
- `RANK`: 排名分析，获取指标排名
- `COMPOSE`: 构成分析，分析指标的构成情况

**实体类型：**
- `Ship`: 船舶
- `Port`: 港口
- `Province`: 省份
- `Basin`: 流域
- `ShippingRoute`: 航线

**时间表达式示例：**
- `R6M`: 最近6个月
- `R3Y`: 最近3年
- `Y2024`: 2024年
- `M202406`: 2024年6月
- `Q2024Q1`: 2024年第1季度
- `M202401_M202406`: 2024年1月到6月

### 3. 实体搜索

智能搜索船舶、港口、航线等实体。

```http
GET /api/v3/search?q=关键词&type=Ship&strategy=fuzzy&limit=10
```

**参数说明：**
- `q`: 搜索关键词（必需）
- `type`: 实体类型（可选）
- `strategy`: 搜索策略（exact/fuzzy/multi，默认fuzzy）
- `limit`: 结果数量限制（默认10）

**响应示例：**
```json
{
  "query": "测试船舶",
  "results": [
    {
      "entity_type": "Ship",
      "identifier": "123456789",
      "display_name": "测试船舶001",
      "description": "MMSI: 123456789, 船东: 测试公司, 载重吨: 5000",
      "score": 0.95,
      "match_type": "fuzzy"
    }
  ],
  "total": 1
}
```

### 4. 船舶画像

获取船舶的详细画像信息。

```http
GET /api/v3/ships/{identifier}/profile?time_expression=R6M&include_history=true&include_cargo=true&include_analysis=true&detail_level=full
```

**参数说明：**
- `identifier`: 船舶标识符（船名或MMSI）
- `time_expression`: 时间表达式（默认R1M）
- `include_history`: 是否包含历史数据（默认false）
- `include_cargo`: 是否包含货物构成（默认false）
- `include_analysis`: 是否包含航线分析（默认false）
- `detail_level`: 详细级别（basic/full，默认basic）

**响应示例：**
```json
{
  "basic_info": {
    "mmsi": "123456789",
    "name": "测试船舶001",
    "owner": "测试公司",
    "dwt": 5000,
    "built_date": "2020-01-01T00:00:00Z"
  },
  "ship_type": {
    "category": "干散货",
    "sub_type": "散货船",
    "sub_code": "0101"
  },
  "recent_stats": {
    "latest_month": "202407",
    "op_ratio": 0.85,
    "voyages": 12,
    "load_ratio": 0.78,
    "capacity_ton": 45000
  },
  "summary": {
    "total_months_data": 6,
    "avg_monthly_capacity": 42000,
    "primary_cargo_types": ["煤炭", "矿石"],
    "main_routes": ["武汉-上海", "南京-重庆"],
    "performance_rating": "良好"
  }
}
```

### 5. 航线画像

获取航线的详细画像信息。

```http
GET /api/v3/routes/{identifier}/profile?time_expression=R6M&include_history=true&include_cargo=true&include_ships=true
```

## 错误处理

API使用标准HTTP状态码，错误响应格式如下：

```json
{
  "error": "错误类型",
  "message": "详细错误信息",
  "code": "错误代码"
}
```

**常见错误代码：**
- `INVALID_REQUEST`: 请求格式错误
- `MISSING_PARAMETER`: 缺少必需参数
- `INVALID_TIME_EXPRESSION`: 时间表达式格式错误
- `SHIP_NOT_FOUND`: 船舶未找到
- `ROUTE_NOT_FOUND`: 航线未找到
- `QUERY_FAILED`: 查询执行失败
- `RATE_LIMIT_EXCEEDED`: 请求频率超限

## 性能优化

### 缓存策略
- API支持查询结果缓存，可通过`cache_enabled`选项控制
- 缓存时间根据数据类型和查询复杂度自动调整

### 请求限制
- 每个IP每分钟最多60次请求
- 请求体大小限制为10MB
- 查询超时时间为30秒

### 最佳实践
1. **合理使用时间范围**：避免查询过长的时间范围
2. **按需获取数据**：使用options参数控制返回的数据详细程度
3. **批量查询**：使用COMPARE或RANK查询类型进行批量分析
4. **缓存结果**：对于相同的查询，启用缓存可显著提升性能

## 示例代码

### JavaScript (Fetch API)
```javascript
// 船舶画像查询
const response = await fetch('http://localhost:8080/api/v3/ships/测试船舶/profile?time_expression=R6M', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
});
const data = await response.json();
console.log(data);

// 统一查询接口
const queryResponse = await fetch('http://localhost:8080/api/v3/query', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    query_type: 'TREND',
    entity: {
      type: 'Ship',
      identifier: '测试船舶'
    },
    metric: '货运量',
    time_expression: 'R12M'
  })
});
const queryData = await queryResponse.json();
console.log(queryData);
```

### Python (requests)
```python
import requests

# 船舶画像查询
response = requests.get(
    'http://localhost:8080/api/v3/ships/测试船舶/profile',
    params={
        'time_expression': 'R6M',
        'include_history': 'true',
        'detail_level': 'full'
    }
)
data = response.json()
print(data)

# 实体搜索
search_response = requests.get(
    'http://localhost:8080/api/v3/search',
    params={
        'q': '船舶',
        'type': 'Ship',
        'limit': 5
    }
)
search_data = search_response.json()
print(search_data)
```

## 版本更新

### v3.1 新特性
- 新增统一查询接口
- 支持C-STEL时间表达式
- 增强的船舶画像功能
- 新增航线维度支持
- 改进的错误处理和日志记录

### 兼容性说明
- v3.1向后兼容v1 API的核心功能
- 建议逐步迁移到v3.1的统一查询接口
- v1 API将在v4.0版本中废弃

## 技术支持

如有问题或建议，请联系技术支持团队。

---

*最后更新：2025年7月30日*
