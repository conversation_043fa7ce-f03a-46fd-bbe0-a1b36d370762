package docs

import "github.com/swaggo/swag"

type s struct{}

func (s *s) ReadDoc() string {
	return doc
}

func init() {
	swag.Register(swag.Name, &s{})
}

const doc = `{
    "schemes": ["http", "https"],
    "swagger": "2.0",
    "info": {
        "description": "长江航运智能分析Agent - REST API 完整实现",
        "title": "Yangtze Shipping Intelligence API",
        "contact": {
            "name": "API Support",
            "email": "<EMAIL>"
        },
        "version": "2.1.0"
    },
    "host": "localhost:8080",
    "basePath": "/api/v1",
    "paths": {
        "/ships/search": {
            "post": {
                "description": "根据船名或MMSI模糊搜索船舶，返回匹配船舶列表",
                "consumes": ["application/json"],
                "produces": ["application/json"],
                "tags": ["船舶"],
                "summary": "搜索船舶",
                "parameters": [
                    {
                        "description": "搜索请求",
                        "name": "search",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/SearchShipsRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "搜索成功",
                        "schema": {
                            "$ref": "#/definitions/ShipSearchResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    }
                }
            }
        },
        "/ships/{ship_id}/profile": {
            "get": {
                "description": "获取指定船舶的详细档案信息",
                "produces": ["application/json"],
                "tags": ["船舶"],
                "summary": "获取船舶档案",
                "parameters": [
                    {
                        "type": "string",
                        "description": "船舶ID",
                        "name": "ship_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查询成功",
                        "schema": {
                            "$ref": "#/definitions/ShipProfileResponse"
                        }
                    },
                    "404": {
                        "description": "船舶未找到",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    }
                }
            }
        },
        "/ships/{ship_id}/stats/monthly": {
            "get": {
                "description": "查询指定船舶在特定月份的聚合运营统计数据",
                "produces": ["application/json"],
                "tags": ["船舶"],
                "summary": "获取船舶月度统计",
                "parameters": [
                    {
                        "type": "string",
                        "description": "船舶ID",
                        "name": "ship_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "时间范围",
                        "name": "time_range",
                        "in": "query",
                        "default": "上个月"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查询成功",
                        "schema": {
                            "$ref": "#/definitions/ShipMonthlyStatsResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    }
                }
            }
        },
        "/ships/{ship_id}/composition/monthly": {
            "get": {
                "description": "查询指定船舶在特定月份运输的各类货物的构成",
                "produces": ["application/json"],
                "tags": ["船舶"],
                "summary": "获取船舶货物构成",
                "parameters": [
                    {
                        "type": "string",
                        "description": "船舶ID",
                        "name": "ship_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "时间范围",
                        "name": "time_range",
                        "in": "query",
                        "default": "上个月"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查询成功",
                        "schema": {
                            "$ref": "#/definitions/ShipMonthlyCompositionResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    }
                }
            }
        },
        "/ships/{ship_id}/lines/monthly": {
            "get": {
                "description": "查询指定船舶在特定月份的所有航线及其统计数据",
                "produces": ["application/json"],
                "tags": ["船舶"],
                "summary": "获取船舶航线统计",
                "parameters": [
                    {
                        "type": "string",
                        "description": "船舶ID",
                        "name": "ship_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "时间范围",
                        "name": "time_range",
                        "in": "query",
                        "default": "上个月"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查询成功",
                        "schema": {
                            "$ref": "#/definitions/ShipMonthlyLineStatsResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    }
                }
            }
        },
        "/ships/{ship_id}/realtime": {
            "get": {
                "description": "查询指定船舶最新的实时动态信息",
                "produces": ["application/json"],
                "tags": ["船舶"],
                "summary": "获取船舶实时状态",
                "parameters": [
                    {
                        "type": "string",
                        "description": "船舶ID",
                        "name": "ship_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查询成功",
                        "schema": {
                            "$ref": "#/definitions/ShipRealtimeStatusResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    }
                }
            }
        },
        "/geo/stats": {
            "get": {
                "description": "查询地理实体在特定时间的整体运营统计数据",
                "produces": ["application/json"],
                "tags": ["地理实体"],
                "summary": "获取地理实体统计",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实体类型 (Port/Province/Basin)",
                        "name": "entity_type",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "实体名称",
                        "name": "entity_name",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "时间范围",
                        "name": "time_range",
                        "in": "query",
                        "default": "上个月"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查询成功",
                        "schema": {
                            "$ref": "#/definitions/GeographicStatsResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    }
                }
            }
        },
        "/geo/composition": {
            "get": {
                "description": "查询地理实体在特定时间内按货物类别分类的详细运量构成",
                "produces": ["application/json"],
                "tags": ["地理实体"],
                "summary": "获取地理实体货物构成",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实体类型 (Port/Province/Basin)",
                        "name": "entity_type",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "实体名称",
                        "name": "entity_name",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "时间范围",
                        "name": "time_range",
                        "in": "query",
                        "default": "上个月"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查询成功",
                        "schema": {
                            "$ref": "#/definitions/GeographicCompositionResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    }
                }
            }
        },
        "/analysis/trend": {
            "post": {
                "description": "获取指定实体的特定指标在一段时间内的趋势数据",
                "consumes": ["application/json"],
                "produces": ["application/json"],
                "tags": ["分析"],
                "summary": "趋势分析",
                "parameters": [
                    {
                        "description": "趋势分析请求",
                        "name": "trend",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/TrendAnalysisRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分析成功",
                        "schema": {
                            "$ref": "#/definitions/MetricTrendResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    }
                }
            }
        },
        "/analysis/compare": {
            "post": {
                "description": "在同一时间、同一指标下，对比两个或多个实体的表现",
                "consumes": ["application/json"],
                "produces": ["application/json"],
                "tags": ["分析"],
                "summary": "对比分析",
                "parameters": [
                    {
                        "description": "对比分析请求",
                        "name": "compare",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/ComparisonRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分析成功",
                        "schema": {
                            "$ref": "#/definitions/MetricComparisonResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    }
                }
            }
        },
        "/analysis/rank": {
            "post": {
                "description": "按指定的运营指标对一类实体进行排名",
                "consumes": ["application/json"],
                "produces": ["application/json"],
                "tags": ["分析"],
                "summary": "排名分析",
                "parameters": [
                    {
                        "description": "排名分析请求",
                        "name": "rank",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/RankingRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "分析成功",
                        "schema": {
                            "$ref": "#/definitions/MetricRankingResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    }
                }
            }
        },
        "/analysis/anomalies": {
            "post": {
                "description": "根据预定义的分析模型检测系统中的异常点",
                "consumes": ["application/json"],
                "produces": ["application/json"],
                "tags": ["分析"],
                "summary": "异常检测",
                "parameters": [
                    {
                        "description": "异常检测请求",
                        "name": "anomaly",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/AnomalyRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "检测成功",
                        "schema": {
                            "$ref": "#/definitions/AnomalyDetectionResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    }
                }
            }
        },
        "/analysis/forecast": {
            "post": {
                "description": "基于历史数据，对某一指标的未来走势进行预测",
                "consumes": ["application/json"],
                "produces": ["application/json"],
                "tags": ["分析"],
                "summary": "预测分析",
                "parameters": [
                    {
                        "description": "预测分析请求",
                        "name": "forecast",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/ForecastRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "预测成功",
                        "schema": {
                            "$ref": "#/definitions/MetricForecastResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    }
                }
            }
        },
        "/metadata/options": {
            "get": {
                "description": "获取某个类别下的可用筛选选项列表",
                "produces": ["application/json"],
                "tags": ["元数据"],
                "summary": "获取选项列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "选项类型 (CargoType/Port/Province/Ship/ShipType/CargoCategory)",
                        "name": "type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查询成功",
                        "schema": {
                            "$ref": "#/definitions/AvailableOptionsResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    }
                }
            }
        },
        "/metadata/entity-types": {
            "get": {
                "description": "获取支持的实体类型列表",
                "produces": ["application/json"],
                "tags": ["元数据"],
                "summary": "获取实体类型",
                "responses": {
                    "200": {
                        "description": "查询成功",
                        "schema": {
                            "$ref": "#/definitions/AvailableOptionsResponse"
                        }
                    }
                }
            }
        },
        "/metadata/metrics": {
            "get": {
                "description": "获取指定实体类型的可用指标列表",
                "produces": ["application/json"],
                "tags": ["元数据"],
                "summary": "获取指标列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "实体类型",
                        "name": "entity_type",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查询成功",
                        "schema": {
                            "$ref": "#/definitions/AvailableOptionsResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/APIError"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "APIResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "object",
                    "description": "响应数据"
                },
                "error": {
                    "type": "string",
                    "description": "错误信息"
                },
                "status": {
                    "type": "string",
                    "description": "响应状态"
                }
            }
        },
        "APIError": {
            "type": "object",
            "properties": {
                "error": {
                    "type": "string"
                },
                "status": {
                    "type": "string",
                    "example": "error"
                }
            }
        },
        "SearchShipsRequest": {
            "type": "object",
            "required": ["search_term"],
            "properties": {
                "search_term": {
                    "type": "string",
                    "description": "搜索关键词",
                    "example": "汉海"
                }
            }
        },
        "ShipSearchResponse": {
            "type": "object",
            "properties": {
                "ships": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/Ship"
                    }
                }
            }
        },
        "Ship": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string",
                    "example": "汉海5号"
                },
                "mmsi": {
                    "type": "string",
                    "example": "413256960"
                },
                "shipId": {
                    "type": "string",
                    "example": "CN20180533386"
                }
            }
        },
        "ShipProfileResponse": {
            "type": "object",
            "properties": {
                "mmsi": {
                    "type": "string",
                    "example": "413256960"
                },
                "name": {
                    "type": "string",
                    "example": "汉海5号"
                },
                "owner": {
                    "type": "string",
                    "example": "A航运公司"
                },
                "dwt": {
                    "type": "number",
                    "example": 16338.00
                },
                "builtDate": {
                    "type": "string",
                    "example": "2020-12-23"
                }
            }
        },
        "ShipMonthlyStatsResponse": {
            "type": "object",
            "properties": {
                "shipId": {
                    "type": "string"
                },
                "time_period": {
                    "type": "string",
                    "example": "202506"
                },
                "opRatio": {
                    "type": "number",
                    "example": 0.92
                },
                "voyages": {
                    "type": "integer",
                    "example": 10
                },
                "turnover_tonkm": {
                    "type": "number",
                    "example": 500000
                },
                "capacity_ton": {
                    "type": "number",
                    "example": 15000
                }
            }
        },
        "ShipMonthlyCompositionResponse": {
            "type": "object",
            "properties": {
                "shipId": {
                    "type": "string"
                },
                "time_period": {
                    "type": "string"
                },
                "composition": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/CargoComposition"
                    }
                }
            }
        },
        "CargoComposition": {
            "type": "object",
            "properties": {
                "cargo_type": {
                    "type": "string",
                    "example": "煤炭及制品"
                },
                "cargo_ton": {
                    "type": "number",
                    "example": 5000
                },
                "percentage": {
                    "type": "number",
                    "example": 0.33
                }
            }
        },
        "ShipMonthlyLineStatsResponse": {
            "type": "object",
            "properties": {
                "shipId": {
                    "type": "string"
                },
                "time_period": {
                    "type": "string"
                },
                "line_stats": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/ShipLineStat"
                    }
                }
            }
        },
        "ShipLineStat": {
            "type": "object",
            "properties": {
                "portO": {
                    "type": "string",
                    "example": "武汉"
                },
                "portD": {
                    "type": "string",
                    "example": "南京"
                },
                "cargo_ton": {
                    "type": "number",
                    "example": 5000
                },
                "turnover_tonkm": {
                    "type": "number",
                    "example": 2500000
                },
                "mileage_km": {
                    "type": "number",
                    "example": 500
                }
            }
        },
        "ShipRealtimeStatusResponse": {
            "type": "object",
            "properties": {
                "mmsi": {
                    "type": "string"
                },
                "ais": {
                    "$ref": "#/definitions/AISData"
                },
                "port_report": {
                    "$ref": "#/definitions/PortReport"
                },
                "lastUpdated": {
                    "type": "string"
                }
            }
        },
        "AISData": {
            "type": "object",
            "properties": {
                "lat": {
                    "type": "number",
                    "example": 30.56
                },
                "lon": {
                    "type": "number",
                    "example": 114.30
                },
                "sog": {
                    "type": "number",
                    "example": 10.2
                },
                "cog": {
                    "type": "number",
                    "example": 95.5
                },
                "navStatus": {
                    "type": "string",
                    "example": "在航(主机推动)"
                },
                "timestamp": {
                    "type": "string",
                    "example": "2025-07-26T10:30:00Z"
                }
            }
        },
        "PortReport": {
            "type": "object",
            "properties": {
                "portStatus": {
                    "type": "string",
                    "example": "在途"
                },
                "reportedPort": {
                    "type": "string",
                    "example": "武汉港"
                },
                "reportTimeIn": {
                    "type": "string",
                    "example": "2025-07-25T08:00:00Z"
                },
                "reportTimeOut": {
                    "type": "string"
                },
                "actualCarryCapacityIn_ton": {
                    "type": "number",
                    "example": 8000
                }
            }
        },
        "GeographicStatsResponse": {
            "type": "object",
            "properties": {
                "entity_type": {
                    "type": "string"
                },
                "entity_name": {
                    "type": "string"
                },
                "time_period": {
                    "type": "string"
                },
                "inShipCount": {
                    "type": "integer"
                },
                "outShipCount": {
                    "type": "integer"
                },
                "totalThroughput_ton": {
                    "type": "number"
                }
            }
        },
        "GeographicCompositionResponse": {
            "type": "object",
            "properties": {
                "entity_name": {
                    "type": "string"
                },
                "time_period": {
                    "type": "string"
                },
                "composition": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/CargoComposition"
                    }
                }
            }
        },
        "TrendAnalysisRequest": {
            "type": "object",
            "required": ["entity_type", "entity_name", "metric", "time_range"],
            "properties": {
                "entity_type": {
                    "type": "string",
                    "description": "实体类型",
                    "example": "Port"
                },
                "entity_name": {
                    "type": "string",
                    "description": "实体名称",
                    "example": "武汉"
                },
                "metric": {
                    "type": "string",
                    "description": "指标名称",
                    "example": "总吞吐量"
                },
                "time_range": {
                    "type": "string",
                    "description": "时间范围",
                    "example": "过去6个月"
                }
            }
        },
        "MetricTrendResponse": {
            "type": "object",
            "properties": {
                "entity_name": {
                    "type": "string"
                },
                "metric": {
                    "type": "string"
                },
                "trend": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/TrendData"
                    }
                }
            }
        },
        "TrendData": {
            "type": "object",
            "properties": {
                "time_period": {
                    "type": "string"
                },
                "value": {
                    "type": "number"
                }
            }
        },
        "ComparisonRequest": {
            "type": "object",
            "required": ["entity_type", "entity_names", "metric", "time_range"],
            "properties": {
                "entity_type": {
                    "type": "string"
                },
                "entity_names": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "metric": {
                    "type": "string"
                },
                "time_range": {
                    "type": "string"
                }
            }
        },
        "MetricComparisonResponse": {
            "type": "object",
            "properties": {
                "metric": {
                    "type": "string"
                },
                "time_period": {
                    "type": "string"
                },
                "comparison": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/ComparisonData"
                    }
                }
            }
        },
        "ComparisonData": {
            "type": "object",
            "properties": {
                "entity_name": {
                    "type": "string"
                },
                "value": {
                    "type": "number"
                }
            }
        },
        "RankingRequest": {
            "type": "object",
            "required": ["entity_type", "time_range", "metric", "top_n"],
            "properties": {
                "entity_type": {
                    "type": "string"
                },
                "time_range": {
                    "type": "string"
                },
                "metric": {
                    "type": "string"
                },
                "top_n": {
                    "type": "integer"
                },
                "cargo_type_filter": {
                    "type": "string"
                }
            }
        },
        "MetricRankingResponse": {
            "type": "object",
            "properties": {
                "ranking": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/RankingData"
                    }
                }
            }
        },
        "RankingData": {
            "type": "object",
            "properties": {
                "rank": {
                    "type": "integer"
                },
                "entity_name": {
                    "type": "string"
                },
                "value": {
                    "type": "number"
                }
            }
        },
        "AnomalyRequest": {
            "type": "object",
            "required": ["analysis_model", "time_range", "threshold_sigma"],
            "properties": {
                "analysis_model": {
                    "type": "string",
                    "example": "ship_op_ratio_anomaly"
                },
                "time_range": {
                    "type": "string"
                },
                "threshold_sigma": {
                    "type": "number",
                    "example": 3.0
                }
            }
        },
        "AnomalyDetectionResponse": {
            "type": "object",
            "properties": {
                "anomalies": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/AnomalyData"
                    }
                }
            }
        },
        "AnomalyData": {
            "type": "object",
            "properties": {
                "entity_name": {
                    "type": "string"
                },
                "metric": {
                    "type": "string"
                },
                "value": {
                    "type": "number"
                },
                "average": {
                    "type": "number"
                },
                "description": {
                    "type": "string"
                }
            }
        },
        "ForecastRequest": {
            "type": "object",
            "required": ["entity_type", "entity_name", "metric", "forecast_horizon"],
            "properties": {
                "entity_type": {
                    "type": "string"
                },
                "entity_name": {
                    "type": "string"
                },
                "metric": {
                    "type": "string"
                },
                "forecast_horizon": {
                    "type": "string"
                }
            }
        },
        "MetricForecastResponse": {
            "type": "object",
            "properties": {
                "forecast": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/ForecastData"
                    }
                }
            }
        },
        "ForecastData": {
            "type": "object",
            "properties": {
                "time_period": {
                    "type": "string"
                },
                "predicted_value": {
                    "type": "number"
                },
                "confidence_upper": {
                    "type": "number"
                },
                "confidence_lower": {
                    "type": "number"
                }
            }
        },
        "AvailableOptionsResponse": {
            "type": "object",
            "properties": {
                "options": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        }
    }
}`