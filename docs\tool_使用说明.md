# tool_.py 使用说明

## 概述

`tool_.py` 是基于最新API设计文档v3.1创建的长江航运智能分析工具模块，提供统一的查询接口和完整的C-STEL时间表达语言支持。

## 核心特性

### 1. 统一查询接口
- **单一入口**: `query_shipping_data` 函数支持所有查询类型
- **多实体支持**: 船舶、港口、省份、流域、航线
- **智能解析**: 自动处理实体标识符和时间表达式

### 2. C-STEL时间表达语言
完整支持C-STEL时间表达语言的所有格式：

#### 原子表达式
- `Y2024` - 2024年
- `Q2024Q1` - 2024年第1季度
- `M202506` - 2025年6月
- `D20240315` - 2024年3月15日（降级到月）

#### 区间表达式
- `M202501_M202506` - 2025年1月到6月
- `Y2023_Y2024` - 2023年到2024年

#### 列表表达式
- `Y2024,Y2025` - 2024年和2025年
- `Q2024Q1,Q2024Q3` - 2024年第1季度和第3季度

#### 相对表达式
- `R6M` - 最近6个月
- `R12M` - 最近12个月

#### 聚合表达式
- `AY2024_Q` - 2024年的每个季度
- `AY2024_M` - 2024年的每个月

#### 当前时间表达式
- `CYTD` - 当年至今
- `CMTD` - 当月至今

### 3. 查询类型支持

#### POINT - 单点查询
查询特定实体在特定时间的指标值
```python
query_shipping_data(
    query_type="POINT",
    entity={"type": "Ship", "identifier": "汉海5号"},
    metric="有效营运率",
    time_expression="M202506"
)
```

#### PROFILE - 画像查询
获取实体的完整画像数据
```python
query_shipping_data(
    query_type="PROFILE",
    entity={"type": "ShippingRoute", "identifier": "武汉-南京航线"},
    metric="ALL",
    time_expression="M202506"
)
```

#### TREND - 趋势分析
分析指标的时间序列变化
```python
query_shipping_data(
    query_type="TREND",
    entity={"type": "Port", "identifier": "武汉港"},
    metric="总吞吐量",
    time_expression="M202501_M202506"
)
```

#### COMPARE - 对比分析
对比多个实体的指标表现
```python
query_shipping_data(
    query_type="COMPARE",
    entity={"type": "Port", "identifier": ["武汉港", "南京港", "芜湖港"]},
    metric="总吞吐量",
    time_expression="M202506"
)
```

#### RANK - 排名分析
按指标对实体进行排序
```python
query_shipping_data(
    query_type="RANK",
    entity={"type": "Ship", "identifier": "长江沿线"},
    metric="有效营运率",
    time_expression="M202506",
    options={"top_n": 10}
)
```

#### COMPOSE - 构成分析
分析实体的内部构成
```python
query_shipping_data(
    query_type="COMPOSE",
    entity={"type": "Ship", "identifier": "汉海5号"},
    metric="货物构成",
    time_expression="M202506",
    options={"dimension": "cargo_type"}
)
```

### 4. 辅助工具函数

#### 实体搜索
```python
search_entities(
    entity_type="Ship",
    search_term="汉海",
    limit=10
)
```

#### 元数据查询
```python
get_metadata("metrics")  # 获取指标列表
get_metadata("entities")  # 获取实体类型
```

#### 实时数据
```python
get_realtime_data("Ship", "413256960")  # 获取船舶实时AIS数据
```

## 架构设计

### 核心组件

1. **CSTELParser**: C-STEL时间表达式解析器
2. **EntityResolver**: 智能实体解析器
3. **APIClient**: 统一API客户端
4. **ShippingDataToolKit**: 主工具包类

### 设计优势

- **模块化设计**: 各组件职责清晰，易于维护
- **错误处理**: 完整的异常捕获和错误响应
- **日志记录**: 详细的操作日志，便于调试
- **缓存支持**: 预留缓存接口，提升性能
- **扩展性**: 易于添加新的查询类型和实体类型

## LLM集成

### Function Calling配置

工具模块提供完整的LLM Function Calling配置：

```python
from src.agent.tool_ import FUNCTION_SCHEMAS

# 4个核心工具函数
# - query_shipping_data: 统一查询接口
# - search_entities: 实体搜索
# - get_metadata: 元数据查询  
# - get_realtime_data: 实时数据
```

### 使用建议

1. **优先使用统一查询**: 大部分需求都可以通过`query_shipping_data`满足
2. **合理选择查询类型**: 根据用户问题选择最合适的查询类型
3. **充分利用C-STEL**: 使用标准化的时间表达式提高查询精度
4. **实体搜索辅助**: 当实体名称不确定时，先使用搜索功能

## 测试和调试

运行测试：
```bash
python src/agent/tool_.py
```

测试内容包括：
- C-STEL解析器功能测试
- 工具函数调用测试
- Function Schemas验证

## 与现有tool.py的对比

| 特性 | tool.py | tool_.py |
|------|---------|----------|
| 查询接口 | 分散的6个函数 | 统一的1个主函数 |
| 时间处理 | 无标准化 | 完整C-STEL支持 |
| 实体类型 | 仅船舶相关 | 5种实体类型 |
| 查询类型 | 实时数据为主 | 6种分析查询 |
| 错误处理 | 基础处理 | 完整错误分类 |
| 扩展性 | 有限 | 高度可扩展 |

## 未来扩展

1. **缓存机制**: 实现智能缓存提升性能
2. **重试机制**: 添加失败重试逻辑
3. **配置化**: 支持配置文件管理
4. **监控指标**: 添加性能监控和统计
5. **更多实体**: 支持船闸、航道等新实体类型

---

**注意**: 当前版本的API端点为模拟地址，实际使用时需要配置正确的后端API地址。
