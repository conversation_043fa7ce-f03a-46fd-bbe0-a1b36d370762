# 智能实体搜索API设计文档

## 1. 概述

### 1.1 设计目标
替代现有的简单RANK查询，提供统一的实体查询接口，支持过滤+排序的复合查询能力，满足复杂业务场景需求。

### 1.2 核心理念
- **搜索即排名**：所有查询都是过滤+排序的组合
- **统一接口**：不区分搜索、排名、过滤，使用统一的查询模式
- **默认排序**：系统自动按业务相关性排序，无需显式指定
- **LLM友好**：过滤条件语义清晰，便于AI理解和生成

## 2. API设计

### 2.1 接口定义
```
POST /api/v3.1/query/entities
```

### 2.2 请求格式
```json
{
  "entity_type": "Ship|Port|ShippingRoute",
  "time_expression": "M202407",
  "filters": {
    // 过滤条件，详见第3节
  },
  "limit": 20
}
```

### 2.3 响应格式
```json
{
  "entity_type": "Ship",
  "time_period": "202407",
  "results": [
    {
      "name": "汉海5号",
      "mmsi": "413256960",
      "dwt": 4500.0,
      "ship_type": "干散货船",
      "province": "湖北省",
      "metrics": {
        "cargo_volume": 15600.0,
        "voyage_count": 8,
        "load_ratio": 0.85
      },
      "routes": ["武汉-南京航线", "武汉-上海航线"],
      "cargo_types": ["煤炭及制品", "矿石"]
    }
  ],
  "total_found": 23,
  "returned": 20,
  "execution_time_ms": 245
}
```

## 3. 过滤条件规范

### 3.1 设计原则
1. **LLM友好**：字段名语义清晰，易于理解
2. **类型一致**：相同类型的过滤条件使用统一格式
3. **可组合**：多个条件可以自由组合
4. **可扩展**：新增条件不影响现有结构

### 3.2 通用过滤条件
```json
{
  // ============ 文本搜索 ============
  "name": "汉海",                    // 实体名称包含
  
  // ============ 数值范围过滤 ============
  "dwt_min": 3000,                  // 最小载重吨
  "dwt_max": 5000,                  // 最大载重吨
  "distance_min": 100,              // 最小距离(km)
  "distance_max": 1000,             // 最大距离(km)
  "build_year_min": 2010,           // 最早建造年份
  "build_year_max": 2020,           // 最晚建造年份
  
  // ============ 分类属性过滤 ============
  "ship_types": ["干散货船", "集装箱船"],     // 船舶类型列表
  "route_types": ["干线", "支线"],          // 航线类型列表
  "cargo_types": ["煤炭及制品", "矿石"],     // 货物类型列表
  "provinces": ["湖北省", "江苏省"],        // 省份列表
  
  // ============ 地理位置过滤 ============
  "origin_ports": ["武汉", "重庆"],         // 起点港口列表
  "destination_ports": ["南京", "上海"],     // 终点港口列表
  "via_ports": ["宜昌", "九江"],           // 经过港口列表
  
  // ============ 运营行为过滤 ============
  "active_routes": ["武汉-南京航线"],       // 活跃航线列表
  "served_ports": ["武汉", "南京"],        // 服务过的港口列表
  "transported_cargos": ["煤炭及制品"],     // 运输过的货物列表
  
  // ============ 性能阈值过滤 ============
  "min_voyages": 3,                 // 最少航次数
  "max_voyages": 50,                // 最多航次数
  "min_cargo_volume": 10000,        // 最少货运量(吨)
  "max_cargo_volume": 100000,       // 最多货运量(吨)
  "min_load_ratio": 0.7,            // 最低装载率(0-1)
  "max_load_ratio": 1.0,            // 最高装载率(0-1)
  "min_ships": 10,                  // 最少船舶数(航线)
  "max_ships": 100,                 // 最多船舶数(航线)
  
  // ============ 排除条件 ============
  "exclude_names": ["停运船舶"],          // 排除的实体名称
  "exclude_types": ["客船"],             // 排除的类型
  "exclude_routes": ["停运航线"],         // 排除的航线
  "exclude_ports": ["关闭港口"]          // 排除的港口
}
```

### 3.3 实体特定过滤条件

#### 3.3.1 Ship实体专用字段
```json
{
  // 船舶标识
  "mmsi": "413256960",              // MMSI号码
  "ship_id": "CN2018...",           // 船舶ID
  
  // 船舶属性
  "owner_companies": ["公司A"],       // 船东公司列表
  "operator_companies": ["公司B"],    // 经营公司列表
  "flag_states": ["中国"],           // 船旗国列表
  "home_ports": ["武汉"],            // 船籍港列表
  
  // 技术规格
  "gross_tonnage_min": 1000,        // 最小总吨位
  "gross_tonnage_max": 10000,       // 最大总吨位
  "length_min": 50,                 // 最小船长(米)
  "length_max": 200,                // 最大船长(米)
  
  // 运营特征
  "frequent_routes": ["常跑航线"],    // 常跑航线(频次>阈值)
  "primary_cargos": ["主要货物"],     // 主要货物(占比>50%)
  
  // 状态过滤
  "operational_status": ["active"],  // 运营状态
  "navigation_area": ["inland"]      // 航行区域
}
```

#### 3.3.2 ShippingRoute实体专用字段
```json
{
  // 航线属性
  "route_codes": ["WH-NJ"],          // 航线代码列表
  "navigation_levels": ["一级"],      // 通航等级列表
  
  // 地理特征
  "crosses_provinces": ["湖北省"],    // 跨越的省份
  "river_sections": ["长江干流"],     // 河段列表
  
  // 运营特征
  "seasonal_routes": true,           // 是否季节性航线
  "traffic_density": "high",         // 交通密度
  
  // 基础设施
  "lock_count_min": 1,               // 最少船闸数
  "lock_count_max": 5,               // 最多船闸数
  "terminal_count_min": 2            // 最少码头数
}
```

## 4. 默认排序规则

### 4.1 Ship实体排序
1. 主排序：`cargo_volume` 降序（货运量）
2. 次排序：`voyage_count` 降序（航次数）
3. 三排序：`load_ratio` 降序（装载率）

### 4.2 ShippingRoute实体排序
1. 主排序：`total_cargo` 降序（总货运量）
2. 次排序：`ship_count` 降序（船舶数量）
3. 三排序：`voyage_count` 降序（航次数）

### 4.3 Port实体排序
1. 主排序：`total_cargo_volume` 降序（总货运量）
2. 次排序：`ship_count` 降序（船舶数量）
3. 三排序：`in_cargo_volume` 降序（进港货量）

## 5. 使用场景示例

### 5.1 基础文本搜索
```json
{
  "entity_type": "Ship",
  "time_expression": "M202407",
  "filters": {
    "name": "汉海"
  },
  "limit": 10
}
```
**场景**：搜索名称包含"汉海"的船舶

### 5.2 属性过滤查询
```json
{
  "entity_type": "Ship",
  "time_expression": "M202407",
  "filters": {
    "dwt_min": 3000,
    "dwt_max": 5000,
    "ship_types": ["干散货船"]
  },
  "limit": 20
}
```
**场景**：查找3000-5000吨级的干散货船

### 5.3 复合条件查询（核心需求）
```json
{
  "entity_type": "Ship",
  "time_expression": "M202407",
  "filters": {
    "dwt_min": 3000,
    "dwt_max": 5000,
    "active_routes": ["武汉-南京航线"],
    "cargo_types": ["煤炭及制品"],
    "min_voyages": 3,
    "min_load_ratio": 0.7
  },
  "limit": 10
}
```
**场景**：查找在武汉-南京航线运输煤炭的3000-5000吨级船舶，至少跑3趟，装载率70%以上

### 5.4 航线查询
```json
{
  "entity_type": "ShippingRoute",
  "time_expression": "M202407",
  "filters": {
    "origin_ports": ["武汉"],
    "route_types": ["干线"],
    "distance_min": 200,
    "distance_max": 800,
    "min_ships": 15
  },
  "limit": 15
}
```
**场景**：查找从武汉出发的干线航线，距离200-800公里，船舶数量15艘以上

### 5.5 纯排名查询
```json
{
  "entity_type": "Ship",
  "time_expression": "M202407",
  "limit": 20
}
```
**场景**：2024年7月货运量TOP20的船舶（无过滤条件）

## 6. 实现架构

### 6.1 技术栈
- **后端框架**：Go + Gin
- **数据库**：Neo4j
- **查询语言**：Cypher
- **缓存**：Redis（可选）

### 6.2 核心组件
```
internal/
├── handlers/
│   └── entity_query_handler.go     # HTTP处理器
├── services/
│   ├── entity_query_service.go     # 查询服务
│   ├── filter_builder.go           # 过滤条件构建器
│   └── query_executor.go           # 查询执行器
├── models/
│   ├── entity_query.go             # 请求/响应模型
│   └── filters.go                  # 过滤条件模型
└── utils/
    └── cypher_builder.go            # Cypher查询构建器
```

### 6.3 查询构建流程
1. **请求解析**：解析JSON请求，验证参数
2. **过滤构建**：根据实体类型和过滤条件构建WHERE子句
3. **查询生成**：生成完整的Cypher查询语句
4. **执行查询**：执行Neo4j查询
5. **结果映射**：将查询结果映射为响应格式
6. **返回响应**：返回JSON响应

## 7. 性能考虑

### 7.1 索引策略
- **Ship节点**：name, mmsi, dwt, regPortProvince
- **ShippingRoute节点**：routeName, routeType, distance_km
- **统计节点**：时间字段(ym)和主要指标字段

### 7.2 查询优化
- **条件顺序**：将选择性高的条件放在前面
- **EXISTS子查询**：用于复杂关联条件
- **LIMIT早期应用**：在排序前应用LIMIT

### 7.3 缓存策略
- **热点查询缓存**：缓存常用的查询结果
- **元数据缓存**：缓存船舶类型、港口列表等元数据
- **TTL设置**：根据数据更新频率设置合适的TTL

## 8. 错误处理

### 8.1 参数验证错误
```json
{
  "error": "Invalid parameter",
  "message": "dwt_min cannot be greater than dwt_max",
  "code": "INVALID_PARAMETER"
}
```

### 8.2 查询执行错误
```json
{
  "error": "Query execution failed",
  "message": "Database connection timeout",
  "code": "QUERY_FAILED"
}
```

### 8.3 业务逻辑错误
```json
{
  "error": "No data found",
  "message": "No entities match the specified criteria",
  "code": "NO_DATA_FOUND"
}
```

## 9. 扩展性设计

### 9.1 新增实体类型
- 定义新的实体过滤条件结构
- 实现对应的查询构建逻辑
- 添加默认排序规则

### 9.2 新增过滤条件
- 在过滤条件结构中添加新字段
- 在过滤构建器中添加对应逻辑
- 更新文档和测试用例

### 9.3 新增排序方式
- 扩展排序字段枚举
- 实现对应的排序逻辑
- 保持向后兼容性

## 10. 迁移策略

### 10.1 替代现有RANK接口
- 保持现有RANK接口可用（标记为deprecated）
- 提供迁移指南和示例
- 逐步迁移现有调用方

### 10.2 向后兼容
- 支持现有RANK查询格式的自动转换
- 提供适配器模式
- 渐进式迁移，避免破坏性变更

## 11. 测试用例设计

### 11.1 Ship实体查询测试用例

#### 测试用例1：基础文本搜索
```json
{
  "test_name": "ship_basic_text_search",
  "description": "船舶名称模糊搜索",
  "request": {
    "entity_type": "Ship",
    "time_expression": "M202407",
    "filters": {
      "name": "汉海"
    },
    "limit": 10
  },
  "expected_conditions": {
    "total_found": ">= 1",
    "results[0].name": "contains '汉海'",
    "results[0].metrics.cargo_volume": "> 0"
  }
}
```

#### 测试用例2：船舶属性过滤
```json
{
  "test_name": "ship_property_filter",
  "description": "按船舶属性过滤",
  "request": {
    "entity_type": "Ship",
    "time_expression": "M202407",
    "filters": {
      "dwt_min": 3000,
      "dwt_max": 5000,
      "ship_types": ["干散货船"]
    },
    "limit": 20
  },
  "expected_conditions": {
    "results[*].dwt": "between 3000 and 5000",
    "results[*].ship_type": "in ['干散货船']",
    "results": "ordered by cargo_volume desc"
  }
}
```

#### 测试用例3：运营行为过滤
```json
{
  "test_name": "ship_operational_filter",
  "description": "按运营行为过滤船舶",
  "request": {
    "entity_type": "Ship",
    "time_expression": "M202407",
    "filters": {
      "active_routes": ["武汉-南京航线"],
      "cargo_types": ["煤炭及制品"],
      "min_voyages": 3,
      "min_cargo_volume": 10000
    },
    "limit": 15
  },
  "expected_conditions": {
    "results[*].metrics.voyage_count": ">= 3",
    "results[*].metrics.cargo_volume": ">= 10000",
    "results[*].routes": "contains '武汉-南京航线'",
    "results[*].cargo_types": "contains '煤炭及制品'"
  }
}
```

#### 测试用例4：复合条件查询
```json
{
  "test_name": "ship_complex_filter",
  "description": "复合条件查询（核心需求场景）",
  "request": {
    "entity_type": "Ship",
    "time_expression": "M202407",
    "filters": {
      "name": "汉海",
      "dwt_min": 3000,
      "dwt_max": 5000,
      "active_routes": ["武汉-南京航线", "武汉-上海航线"],
      "cargo_types": ["煤炭及制品", "矿石"],
      "min_voyages": 5,
      "min_load_ratio": 0.7
    },
    "limit": 10
  },
  "expected_conditions": {
    "results[*].name": "contains '汉海'",
    "results[*].dwt": "between 3000 and 5000",
    "results[*].metrics.voyage_count": ">= 5",
    "results[*].metrics.load_ratio": ">= 0.7"
  }
}
```

### 11.2 ShippingRoute实体查询测试用例

#### 测试用例5：航线基础搜索
```json
{
  "test_name": "route_basic_search",
  "description": "航线名称搜索",
  "request": {
    "entity_type": "ShippingRoute",
    "time_expression": "M202407",
    "filters": {
      "name": "武汉"
    },
    "limit": 10
  },
  "expected_conditions": {
    "results[*].route_name": "contains '武汉'",
    "results[*].metrics.total_cargo": "> 0"
  }
}
```

#### 测试用例6：航线属性过滤
```json
{
  "test_name": "route_property_filter",
  "description": "按航线属性过滤",
  "request": {
    "entity_type": "ShippingRoute",
    "time_expression": "M202407",
    "filters": {
      "route_types": ["干线"],
      "distance_min": 200,
      "distance_max": 800,
      "origin_ports": ["武汉"],
      "min_ships": 10
    },
    "limit": 15
  },
  "expected_conditions": {
    "results[*].route_type": "== '干线'",
    "results[*].distance_km": "between 200 and 800",
    "results[*].origin_port": "== '武汉'",
    "results[*].metrics.ship_count": ">= 10"
  }
}
```

### 11.3 边界条件测试用例

#### 测试用例7：空过滤条件
```json
{
  "test_name": "empty_filters",
  "description": "无过滤条件的纯排名查询",
  "request": {
    "entity_type": "Ship",
    "time_expression": "M202407",
    "limit": 20
  },
  "expected_conditions": {
    "total_found": "> 0",
    "returned": "<= 20",
    "results": "ordered by cargo_volume desc"
  }
}
```

#### 测试用例8：无结果查询
```json
{
  "test_name": "no_results",
  "description": "过滤条件过严导致无结果",
  "request": {
    "entity_type": "Ship",
    "time_expression": "M202407",
    "filters": {
      "dwt_min": 100000,
      "cargo_types": ["不存在的货物类型"]
    },
    "limit": 10
  },
  "expected_conditions": {
    "total_found": "== 0",
    "returned": "== 0",
    "results": "== []"
  }
}
```

#### 测试用例9：参数验证
```json
{
  "test_name": "parameter_validation",
  "description": "参数验证测试",
  "request": {
    "entity_type": "Ship",
    "time_expression": "M202407",
    "filters": {
      "dwt_min": 5000,
      "dwt_max": 3000
    },
    "limit": 10
  },
  "expected_response": {
    "status": "error",
    "error": "Invalid parameter",
    "message": "dwt_min cannot be greater than dwt_max"
  }
}
```

### 11.4 性能测试用例

#### 测试用例10：大数据量查询
```json
{
  "test_name": "large_dataset_query",
  "description": "大数据量查询性能测试",
  "request": {
    "entity_type": "Ship",
    "time_expression": "M202407",
    "filters": {
      "dwt_min": 1000
    },
    "limit": 100
  },
  "performance_expectations": {
    "execution_time_ms": "< 1000",
    "memory_usage": "< 100MB",
    "cpu_usage": "< 50%"
  }
}
```

## 12. 实现优先级

### 12.1 第一阶段（P0 - 核心功能）
- ✅ Ship实体基础查询
- ✅ 核心过滤条件：name, dwt_range, ship_types, active_routes, cargo_types
- ✅ 默认排序实现
- ✅ 基础错误处理

### 12.2 第二阶段（P1 - 增强功能）
- ✅ ShippingRoute和Port实体查询
- ✅ 高级过滤条件：地理位置、性能阈值
- ✅ 详细响应格式（包含breakdown数据）
- ✅ 性能优化

### 12.3 第三阶段（P2 - 扩展功能）
- ✅ 排除条件支持
- ✅ 复杂关联查询
- ✅ 缓存机制
- ✅ 监控和日志

## 13. 成功标准

### 13.1 功能标准
- ✅ 支持Ship、Route、Port三种实体类型查询
- ✅ 支持15+种核心过滤条件
- ✅ 支持复合过滤条件组合
- ✅ 默认排序符合业务逻辑
- ✅ 完整的错误处理和参数验证

### 13.2 性能标准
- ✅ 单次查询响应时间 < 500ms
- ✅ 支持并发查询 > 100 QPS
- ✅ 内存使用 < 50MB per request
- ✅ 99%查询成功率

### 13.3 质量标准
- ✅ 单元测试覆盖率 > 90%
- ✅ 集成测试通过率 100%
- ✅ API文档完整准确
- ✅ 代码review通过

---

**文档版本**：v1.0
**创建日期**：2024-08-05
**最后更新**：2024-08-05
**维护者**：开发团队
