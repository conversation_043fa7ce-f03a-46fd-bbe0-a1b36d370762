# 智能实体查询API使用指南

## 概述

智能实体查询API是一个统一的实体查询接口，支持复杂的过滤条件和自动排序，替代了原有的简单RANK查询。

## 接口地址

```
POST /api/v3.1/query/entities
```

## 基本用法

### 1. 基础船舶搜索

搜索名称包含"汉海"的船舶：

```bash
curl -X POST http://localhost:8080/api/v3.1/query/entities \
  -H "Content-Type: application/json" \
  -d '{
    "entity_type": "Ship",
    "time_expression": "M202407",
    "filters": {
      "name": "汉海"
    },
    "limit": 10
  }'
```

### 2. 船舶属性过滤

查找3000-5000吨级的干散货船：

```bash
curl -X POST http://localhost:8080/api/v3.1/query/entities \
  -H "Content-Type: application/json" \
  -d '{
    "entity_type": "Ship",
    "time_expression": "M202407",
    "filters": {
      "dwt_min": 3000,
      "dwt_max": 5000,
      "ship_types": ["干散货船"]
    },
    "limit": 20
  }'
```

### 3. 复合条件查询（核心需求）

查找在武汉-南京航线运输煤炭的3000-5000吨级船舶，至少跑3趟，装载率70%以上：

```bash
curl -X POST http://localhost:8080/api/v3.1/query/entities \
  -H "Content-Type: application/json" \
  -d '{
    "entity_type": "Ship",
    "time_expression": "M202407",
    "filters": {
      "dwt_min": 3000,
      "dwt_max": 5000,
      "active_routes": ["武汉-南京航线"],
      "cargo_types": ["煤炭及制品"],
      "min_voyages": 3,
      "min_load_ratio": 0.7
    },
    "limit": 10
  }'
```

### 4. 航线查询

查找从武汉出发的干线航线，距离200-800公里，船舶数量10艘以上：

```bash
curl -X POST http://localhost:8080/api/v3.1/query/entities \
  -H "Content-Type: application/json" \
  -d '{
    "entity_type": "ShippingRoute",
    "time_expression": "M202407",
    "filters": {
      "origin_ports": ["武汉"],
      "route_types": ["干线"],
      "distance_min": 200,
      "distance_max": 800,
      "min_ships": 10
    },
    "limit": 15
  }'
```

### 5. 港口查询

查找湖北省的港口，货运量超过10万吨：

```bash
curl -X POST http://localhost:8080/api/v3.1/query/entities \
  -H "Content-Type: application/json" \
  -d '{
    "entity_type": "Port",
    "time_expression": "M202407",
    "filters": {
      "provinces": ["湖北省"],
      "min_cargo_volume": 100000
    },
    "limit": 10
  }'
```

### 6. 纯排名查询

无过滤条件的纯排名查询（替代原有RANK）：

```bash
curl -X POST http://localhost:8080/api/v3.1/query/entities \
  -H "Content-Type: application/json" \
  -d '{
    "entity_type": "Ship",
    "time_expression": "M202407",
    "limit": 20
  }'
```

## 响应格式

```json
{
  "entity_type": "Ship",
  "time_period": "202407",
  "results": [
    {
      "name": "汉海5号",
      "identifier": "413256960",
      "entity_type": "Ship",
      "properties": {
        "mmsi": "413256960",
        "dwt": 4500.0,
        "province": "湖北省"
      },
      "metrics": {
        "cargo_volume": 15600.0,
        "voyage_count": 8,
        "load_ratio": 0.85
      },
      "routes": ["武汉-南京航线", "武汉-上海航线"],
      "cargo_types": ["煤炭及制品", "矿石"]
    }
  ],
  "total_found": 23,
  "returned": 20,
  "execution_time_ms": 245
}
```

## 支持的过滤条件

### 通用过滤条件

- `name`: 文本搜索
- `dwt_min/dwt_max`: 载重吨范围
- `distance_min/distance_max`: 距离范围
- `build_year_min/build_year_max`: 建造年份范围
- `ship_types`: 船舶类型列表
- `route_types`: 航线类型列表
- `cargo_types`: 货物类型列表
- `provinces`: 省份列表
- `origin_ports`: 起点港口列表
- `destination_ports`: 终点港口列表
- `active_routes`: 活跃航线列表
- `min_voyages/max_voyages`: 航次数范围
- `min_cargo_volume/max_cargo_volume`: 货运量范围
- `min_load_ratio/max_load_ratio`: 装载率范围
- `min_ships/max_ships`: 船舶数量范围

### 实体特定过滤条件

#### Ship专用
- `mmsi`: MMSI号码
- `ship_id`: 船舶ID
- `owner_companies`: 船东公司列表
- `home_ports`: 船籍港列表

#### Route专用
- `route_codes`: 航线代码列表
- `navigation_levels`: 通航等级列表

### 排除条件
- `exclude_names`: 排除的实体名称
- `exclude_types`: 排除的类型
- `exclude_routes`: 排除的航线
- `exclude_ports`: 排除的港口

## 默认排序规则

### Ship实体
1. 主排序：货运量降序
2. 次排序：航次数降序
3. 三排序：装载率降序

### ShippingRoute实体
1. 主排序：总货运量降序
2. 次排序：船舶数量降序
3. 三排序：航次数降序

### Port实体
1. 主排序：总货运量降序
2. 次排序：船舶数量降序
3. 三排序：进港货量降序

## 错误处理

### 参数验证错误
```json
{
  "error": "Invalid parameter",
  "message": "dwt_min cannot be greater than dwt_max",
  "code": "INVALID_PARAMETER"
}
```

### 查询执行错误
```json
{
  "error": "Entity query failed",
  "message": "Database connection timeout",
  "code": "QUERY_FAILED"
}
```

## 性能建议

1. **合理设置limit**：建议不超过100
2. **使用具体的过滤条件**：避免过于宽泛的查询
3. **优先使用索引字段**：如name、mmsi、dwt等
4. **避免复杂的EXISTS查询**：如非必要，避免同时使用多个关联过滤条件

## 迁移指南

### 从RANK查询迁移

原有RANK查询：
```json
{
  "query_type": "RANK",
  "entity": {"type": "Ship"},
  "metric": "货运量",
  "time_expression": "M202407",
  "limit": 20
}
```

新的智能查询：
```json
{
  "entity_type": "Ship",
  "time_expression": "M202407",
  "limit": 20
}
```

### 从搜索查询迁移

原有搜索查询：
```json
{
  "query": "汉海",
  "entity_type": "Ship",
  "limit": 10
}
```

新的智能查询：
```json
{
  "entity_type": "Ship",
  "time_expression": "M202407",
  "filters": {
    "name": "汉海"
  },
  "limit": 10
}
```

## 注意事项

1. **时间表达式必需**：所有查询都需要提供time_expression
2. **实体类型必需**：必须指定entity_type
3. **数值范围验证**：min值不能大于max值
4. **过滤条件组合**：多个条件之间是AND关系
5. **结果排序**：系统自动按业务相关性排序，无需手动指定
