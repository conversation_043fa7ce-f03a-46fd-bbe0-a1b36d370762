package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"neoapi/internal/models"
	"neoapi/internal/services"
)

// MetricHandler 指标管理处理器
type MetricHandler struct {
	metricService *services.MetricService
}

// NewMetricHandler 创建新的指标处理器
func NewMetricHandler() *MetricHandler {
	return &MetricHandler{
		metricService: services.NewMetricService(),
	}
}

// GetMetrics 获取指标列表
// GET /api/metrics?entity_type=Ship&category=运营效率&search=营运
func (h *MetricHandler) GetMetrics(c *gin.Context) {
	entityTypeStr := c.Query("entity_type")
	category := c.Query("category")
	search := c.Query("search")
	limitStr := c.Default<PERSON>uery("limit", "50")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 50
	}

	var metrics []*services.MetricInfo
	var responseErr error

	// 根据不同的查询条件获取指标
	if search != "" {
		// 搜索指标
		configs := h.metricService.SearchMetrics(search)
		for _, config := range configs {
			if len(metrics) >= limit {
				break
			}
			metrics = append(metrics, &services.MetricInfo{
				ChineseName: config.ChineseName,
				EnglishName: config.EnglishName,
				DBField:     config.DBField,
				Unit:        config.Unit,
				Description: config.Description,
				Category:    config.Category,
				EntityTypes: config.EntityTypes,
				Aliases:     config.Aliases,
			})
		}
	} else if entityTypeStr != "" {
		// 按实体类型获取指标
		entityType := models.EntityType(entityTypeStr)
		metrics, responseErr = h.metricService.GetEntityMetricsInfo(entityType)
	} else if category != "" {
		// 按分类获取指标
		configs := h.metricService.GetMetricsByCategory(category)
		for _, config := range configs {
			if len(metrics) >= limit {
				break
			}
			metrics = append(metrics, &services.MetricInfo{
				ChineseName: config.ChineseName,
				EnglishName: config.EnglishName,
				DBField:     config.DBField,
				Unit:        config.Unit,
				Description: config.Description,
				Category:    config.Category,
				EntityTypes: config.EntityTypes,
				Aliases:     config.Aliases,
			})
		}
	} else {
		// 获取所有指标（分页）
		allEntityTypes := []models.EntityType{
			models.EntityTypeShip,
			models.EntityTypePort,
			models.EntityTypeShippingRoute,
		}
		
		for _, entityType := range allEntityTypes {
			entityMetrics, err := h.metricService.GetEntityMetricsInfo(entityType)
			if err == nil {
				metrics = append(metrics, entityMetrics...)
			}
			if len(metrics) >= limit {
				break
			}
		}
	}

	if responseErr != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "查询失败",
			"message": responseErr.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"metrics": metrics,
		"total":   len(metrics),
		"limit":   limit,
	})
}

// GetMetricInfo 获取单个指标详细信息
// GET /api/metrics/:name
func (h *MetricHandler) GetMetricInfo(c *gin.Context) {
	metricName := c.Param("name")
	if metricName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "指标名称不能为空",
		})
		return
	}

	metricInfo, err := h.metricService.GetMetricInfo(metricName)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "指标不存在",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, metricInfo)
}

// GetCategories 获取所有指标分类
// GET /api/metrics/categories
func (h *MetricHandler) GetCategories(c *gin.Context) {
	categories := h.metricService.GetAllCategories()
	
	c.JSON(http.StatusOK, gin.H{
		"categories": categories,
		"total":      len(categories),
	})
}

// GetSuggestions 获取指标建议（用于自动补全）
// GET /api/metrics/suggestions?input=营运&entity_type=Ship&limit=10
func (h *MetricHandler) GetSuggestions(c *gin.Context) {
	input := c.Query("input")
	entityTypeStr := c.Query("entity_type")
	limitStr := c.DefaultQuery("limit", "10")

	if input == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "输入不能为空",
		})
		return
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 10
	}

	entityType := models.EntityType(entityTypeStr)
	if entityType == "" {
		entityType = models.EntityTypeShip // 默认为船舶
	}

	suggestions := h.metricService.GetMetricSuggestions(input, entityType, limit)

	c.JSON(http.StatusOK, gin.H{
		"suggestions": suggestions,
		"input":       input,
		"entity_type": entityType,
		"total":       len(suggestions),
	})
}

// ValidateMetric 验证指标是否适用于指定实体类型
// POST /api/metrics/validate
func (h *MetricHandler) ValidateMetric(c *gin.Context) {
	var req struct {
		MetricName string             `json:"metric_name" binding:"required"`
		EntityType models.EntityType `json:"entity_type" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	err := h.metricService.ValidateMetric(req.MetricName, req.EntityType)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"valid":   false,
			"error":   err.Error(),
			"metric":  req.MetricName,
			"entity":  req.EntityType,
		})
		return
	}

	// 获取标准化的指标信息
	metricInfo, err := h.metricService.GetMetricInfo(req.MetricName)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取指标信息失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"valid":       true,
		"metric":      req.MetricName,
		"entity":      req.EntityType,
		"metric_info": metricInfo,
	})
}

// RefreshMetrics 刷新指标注册表
// POST /api/metrics/refresh
func (h *MetricHandler) RefreshMetrics(c *gin.Context) {
	h.metricService.RefreshRegistry()
	
	c.JSON(http.StatusOK, gin.H{
		"message": "指标注册表已刷新",
		"timestamp": "now",
	})
}

// GetEntityTypes 获取支持的实体类型
// GET /api/metrics/entity-types
func (h *MetricHandler) GetEntityTypes(c *gin.Context) {
	entityTypes := []models.EntityType{
		models.EntityTypeShip,
		models.EntityTypePort,
		models.EntityTypeShippingRoute,
		models.EntityTypeProvince,
		models.EntityTypeBasin,
	}

	c.JSON(http.StatusOK, gin.H{
		"entity_types": entityTypes,
		"total":        len(entityTypes),
	})
}
