package handlers

import (
	"encoding/json"
	"log"
	"net/http"
	"strconv"

	"neoapi/internal/models"
	"neoapi/internal/services"
	"neoapi/internal/time"

	"github.com/gin-gonic/gin"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
)

// UnifiedHandler 统一API处理器
type UnifiedHandler struct {
	queryService        *services.QueryService
	entitySearchService *services.EntitySearchService
	entityQueryService  *services.EntityQueryService
	routeService        *services.RouteService
	metricService       *services.MetricService
}

// NewUnifiedHandler 创建新的统一处理器
func NewUnifiedHandler(driver neo4j.DriverWithContext) *UnifiedHandler {
	return &UnifiedHandler{
		queryService:        services.NewQueryService(driver),
		entitySearchService: services.NewEntitySearchService(driver),
		entityQueryService:  services.NewEntityQueryService(driver),
		routeService:        services.NewRouteService(driver),
		metricService:       services.NewMetricService(),
	}
}

// UnifiedQuery 统一查询接口
// @Summary 统一查询接口
// @Description 支持POINT、PROFILE、TREND、COMPARE、RANK、COMPOSE等查询类型
// @Tags Query
// @Accept json
// @Produce json
// @Param request body models.UnifiedQueryRequest true "查询请求"
// @Success 200 {object} models.UnifiedQueryResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/query [post]
func (h *UnifiedHandler) UnifiedQuery(c *gin.Context) {
	var req models.UnifiedQueryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    "INVALID_REQUEST",
		})
		return
	}

	// 执行查询
	response, err := h.queryService.ExecuteQuery(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Query execution failed",
			Message: err.Error(),
			Code:    "QUERY_FAILED",
		})
		return
	}

	// 打印格式化的JSON响应结果
	if jsonBytes, err := json.MarshalIndent(response, "", "  "); err == nil {
		log.Printf("[UnifiedQuery] Response JSON:\n%s", string(jsonBytes))
	} else {
		log.Printf("[UnifiedQuery] Failed to marshal response to JSON: %v", err)
	}

	c.JSON(http.StatusOK, response)
}

// CoreQuery 核心查询接口 (POINT, PROFILE, TREND)
// @Summary 核心查询接口
// @Description 支持POINT、PROFILE、TREND等高频查询类型，优化性能
// @Tags Core
// @Accept json
// @Produce json
// @Param request body models.UnifiedQueryRequest true "查询请求"
// @Success 200 {object} models.UnifiedQueryResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/core/query [post]
func (h *UnifiedHandler) CoreQuery(c *gin.Context) {
	var req models.UnifiedQueryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    "INVALID_REQUEST",
		})
		return
	}

	// 验证查询类型是否为核心查询类型
	if req.QueryType != models.QueryTypePoint &&
		req.QueryType != models.QueryTypeProfile &&
		req.QueryType != models.QueryTypeTrend {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid query type for core endpoint",
			Message: "Core endpoint only supports POINT, PROFILE, and TREND queries",
			Code:    "INVALID_QUERY_TYPE",
		})
		return
	}

	// 执行查询
	response, err := h.queryService.ExecuteQuery(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Core query execution failed",
			Message: err.Error(),
			Code:    "CORE_QUERY_FAILED",
		})
		return
	}

	// 打印格式化的JSON响应结果
	if jsonBytes, err := json.MarshalIndent(response, "", "  "); err == nil {
		log.Printf("[CoreQuery] Response JSON:\n%s", string(jsonBytes))
	} else {
		log.Printf("[CoreQuery] Failed to marshal response to JSON: %v", err)
	}

	c.JSON(http.StatusOK, response)
}

// AnalyticsQuery 分析查询接口 (COMPARE, RANK, COMPOSE)
// @Summary 分析查询接口
// @Description 支持COMPARE、RANK、COMPOSE等分析型查询类型
// @Tags Analytics
// @Accept json
// @Produce json
// @Param request body models.UnifiedQueryRequest true "查询请求"
// @Success 200 {object} models.UnifiedQueryResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/analytics/query [post]
func (h *UnifiedHandler) AnalyticsQuery(c *gin.Context) {
	var req models.UnifiedQueryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    "INVALID_REQUEST",
		})
		return
	}

	// 验证查询类型是否为分析查询类型
	if req.QueryType != models.QueryTypeCompare &&
		req.QueryType != models.QueryTypeRank &&
		req.QueryType != models.QueryTypeCompose {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid query type for analytics endpoint",
			Message: "Analytics endpoint only supports COMPARE, RANK, and COMPOSE queries",
			Code:    "INVALID_QUERY_TYPE",
		})
		return
	}

	// 执行查询
	response, err := h.queryService.ExecuteQuery(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Analytics query execution failed",
			Message: err.Error(),
			Code:    "ANALYTICS_QUERY_FAILED",
		})
		return
	}

	// 打印格式化的JSON响应结果
	if jsonBytes, err := json.MarshalIndent(response, "", "  "); err == nil {
		log.Printf("[AnalyticsQuery] Response JSON:\n%s", string(jsonBytes))
	} else {
		log.Printf("[AnalyticsQuery] Failed to marshal response to JSON: %v", err)
	}

	c.JSON(http.StatusOK, response)
}

// SearchEntities 实体搜索接口 (POST)
// @Summary 实体搜索接口
// @Description 智能搜索船舶、港口、航线等实体，支持复杂搜索条件
// @Tags Search
// @Accept json
// @Produce json
// @Param request body services.SearchRequest true "搜索请求"
// @Success 200 {object} services.SearchResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/search/entities [post]
func (h *UnifiedHandler) SearchEntities(c *gin.Context) {
	var req services.SearchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    "INVALID_REQUEST",
		})
		return
	}

	// 验证必需参数
	if req.Query == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing search query",
			Message: "Query field is required",
			Code:    "MISSING_PARAMETER",
		})
		return
	}

	// 设置默认值
	if req.Strategy == "" {
		req.Strategy = models.ResolutionFuzzy
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}

	// 执行搜索
	response, err := h.entitySearchService.SearchEntities(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Search failed",
			Message: err.Error(),
			Code:    "SEARCH_FAILED",
		})
		return
	}

	// 打印格式化的JSON响应结果
	if jsonBytes, err := json.MarshalIndent(response, "", "  "); err == nil {
		log.Printf("[SearchEntities] Response JSON:\n%s", string(jsonBytes))
	} else {
		log.Printf("[SearchEntities] Failed to marshal response to JSON: %v", err)
	}

	c.JSON(http.StatusOK, response)
}

// SearchEntitiesLegacy 实体搜索接口 (GET) - 兼容性支持
// @Summary 实体搜索接口 (GET)
// @Description 智能搜索船舶、港口、航线等实体，使用查询参数
// @Tags Search
// @Accept json
// @Produce json
// @Param q query string true "搜索关键词"
// @Param type query string false "实体类型" Enums(Ship,Port,Province,Basin,ShippingRoute)
// @Param strategy query string false "搜索策略" Enums(exact,fuzzy,multi)
// @Param limit query int false "结果数量限制" default(10)
// @Success 200 {object} services.SearchResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/search/entities [get]
func (h *UnifiedHandler) SearchEntitiesLegacy(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing search query",
			Message: "Parameter 'q' is required",
			Code:    "MISSING_PARAMETER",
		})
		return
	}

	req := &services.SearchRequest{
		Query:    query,
		Strategy: models.ResolutionFuzzy, // 默认模糊搜索
		Limit:    10,                     // 默认限制10个结果
	}

	// 解析实体类型
	if entityType := c.Query("type"); entityType != "" {
		req.EntityType = models.EntityType(entityType)
	}

	// 解析搜索策略
	if strategy := c.Query("strategy"); strategy != "" {
		req.Strategy = models.ResolutionStrategy(strategy)
	}

	// 解析结果数量限制
	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 {
			req.Limit = limit
		}
	}

	// 执行搜索
	response, err := h.entitySearchService.SearchEntities(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Search failed",
			Message: err.Error(),
			Code:    "SEARCH_FAILED",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// QueryEntities 智能实体查询接口
// @Summary 智能实体查询接口
// @Description 支持复杂过滤条件的实体查询，替代简单的RANK查询
// @Tags EntityQuery
// @Accept json
// @Produce json
// @Param request body models.EntityQueryRequest true "实体查询请求"
// @Success 200 {object} models.EntityQueryResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v3.1/query/entities [post]
func (h *UnifiedHandler) QueryEntities(c *gin.Context) {
	var req models.EntityQueryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    "INVALID_REQUEST",
		})
		return
	}

	// 执行智能实体查询
	response, err := h.entityQueryService.QueryEntities(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Entity query failed",
			Message: err.Error(),
			Code:    "QUERY_FAILED",
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetShipProfile 获取船舶画像
// @Summary 获取船舶画像
// @Description 获取船舶的详细画像信息
// @Tags Profile
// @Produce json
// @Param identifier path string true "船舶标识符（船名或MMSI）"
// @Param time_expression query string true "时间表达式" example("R6M")
// @Param include_history query bool false "是否包含历史数据" default(false)
// @Param include_cargo query bool false "是否包含货物构成" default(false)
// @Param include_analysis query bool false "是否包含航线分析" default(false)
// @Param detail_level query string false "详细级别" Enums(basic,full) default(basic)
// @Success 200 {object} models.ShipProfileData
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/profiles/ships/{identifier} [get]
func (h *UnifiedHandler) GetShipProfile(c *gin.Context) {
	identifier := c.Param("identifier")
	timeExpression := c.Query("time_expression")

	// 记录请求详细信息
	log.Printf("[GetShipProfile] Request - identifier: '%s', time_expression: '%s', full_path: '%s'",
		identifier, timeExpression, c.Request.URL.String())

	if identifier == "" {
		log.Printf("[GetShipProfile] Error - Missing ship identifier, full_path: '%s'", c.Request.URL.String())
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing ship identifier",
			Message: "Ship identifier is required",
			Code:    "MISSING_PARAMETER",
		})
		return
	}

	if timeExpression == "" {
		timeExpression = "R1M" // 默认最近1个月
		log.Printf("[GetShipProfile] Using default time expression: %s", timeExpression)
	}

	// 构建统一查询请求
	req := &models.UnifiedQueryRequest{
		QueryType: models.QueryTypeProfile,
		Entity: models.EntityRequest{
			Type:               models.EntityTypeShip,
			Identifier:         identifier,
			ResolutionStrategy: models.ResolutionFuzzy,
		},
		Metric:         "船舶画像",
		TimeExpression: timeExpression,
	}

	// 记录查询请求详情
	log.Printf("[GetShipProfile] Executing query - QueryType: %s, Entity: %+v, Metric: %s, TimeExpression: %s",
		req.QueryType, req.Entity, req.Metric, req.TimeExpression)

	// 执行查询
	response, err := h.queryService.ExecuteQuery(c.Request.Context(), req)
	if err != nil {
		log.Printf("[GetShipProfile] Query execution failed - identifier: '%s', error: %v", identifier, err)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get ship profile",
			Message: err.Error(),
			Code:    "PROFILE_FAILED",
		})
		return
	}

	if response.Status == "error" {
		log.Printf("[GetShipProfile] Query returned error - identifier: '%s', error: %s", identifier, response.Error)
		if response.Error == "ship not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "Ship not found",
				Message: response.Error,
				Code:    "SHIP_NOT_FOUND",
			})
			return
		}
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Profile query failed",
			Message: response.Error,
			Code:    "PROFILE_ERROR",
		})
		return
	}

	log.Printf("[GetShipProfile] Query successful - identifier: '%s'", identifier)

	// 打印格式化的JSON响应结果
	if jsonBytes, err := json.MarshalIndent(response.Data, "", "  "); err == nil {
		log.Printf("[GetShipProfile] Response JSON:\n%s", string(jsonBytes))
	} else {
		log.Printf("[GetShipProfile] Failed to marshal response to JSON: %v", err)
	}

	c.JSON(http.StatusOK, response.Data)
}

// GetRouteProfile 获取航线画像
// @Summary 获取航线画像
// @Description 获取航线的详细画像信息
// @Tags Profile
// @Produce json
// @Param identifier path string true "航线标识符（航线名或航线ID）"
// @Param time_expression query string true "时间表达式" example("R6M")
// @Param include_history query bool false "是否包含历史数据" default(false)
// @Param include_cargo query bool false "是否包含货物构成" default(false)
// @Param include_ships query bool false "是否包含船舶分析" default(false)
// @Success 200 {object} services.RouteProfileData
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/profiles/routes/{identifier} [get]
func (h *UnifiedHandler) GetRouteProfile(c *gin.Context) {
	identifier := c.Param("identifier")
	timeExpression := c.Query("time_expression")

	if identifier == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing route identifier",
			Message: "Route identifier is required",
			Code:    "MISSING_PARAMETER",
		})
		return
	}

	if timeExpression == "" {
		timeExpression = "R1M" // 默认最近1个月
	}

	// 解析时间表达式
	timeParser := time.NewCSTELParser()
	timeQuery, err := timeParser.Parse(timeExpression)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid time expression",
			Message: err.Error(),
			Code:    "INVALID_TIME_EXPRESSION",
		})
		return
	}

	// 获取航线画像
	profile, err := h.routeService.GetRouteProfile(
		c.Request.Context(),
		identifier,
		timeQuery,
		c.Query("include_history") == "true",
		c.Query("include_cargo") == "true",
		c.Query("include_ships") == "true",
	)
	if err != nil {
		if err.Error() == "route not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "Route not found",
				Message: err.Error(),
				Code:    "ROUTE_NOT_FOUND",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get route profile",
			Message: err.Error(),
			Code:    "PROFILE_FAILED",
		})
		return
	}

	c.JSON(http.StatusOK, profile)
}

// GetPortProfile 获取港口画像
// @Summary 获取港口画像
// @Description 获取港口的详细画像信息
// @Tags Profile
// @Produce json
// @Param identifier path string true "港口标识符（港口名）"
// @Param time_expression query string true "时间表达式" example("R6M")
// @Param include_history query bool false "是否包含历史数据" default(false)
// @Param include_cargo query bool false "是否包含货物构成" default(false)
// @Param include_analysis query bool false "是否包含分析数据" default(false)
// @Param detail_level query string false "详细级别" Enums(basic,full) default(basic)
// @Success 200 {object} models.PortProfileData
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/profiles/ports/{identifier} [get]
func (h *UnifiedHandler) GetPortProfile(c *gin.Context) {
	identifier := c.Param("identifier")
	timeExpression := c.Query("time_expression")

	// 记录请求详细信息
	log.Printf("[GetPortProfile] Request - identifier: '%s', time_expression: '%s', full_path: '%s'",
		identifier, timeExpression, c.Request.URL.String())

	if identifier == "" {
		log.Printf("[GetPortProfile] Error - Missing port identifier, full_path: '%s'", c.Request.URL.String())
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing port identifier",
			Message: "Port identifier is required",
			Code:    "MISSING_PARAMETER",
		})
		return
	}

	if timeExpression == "" {
		timeExpression = "R1M" // 默认最近1个月
		log.Printf("[GetPortProfile] Using default time expression: %s", timeExpression)
	}

	// 构建统一查询请求
	req := &models.UnifiedQueryRequest{
		QueryType: models.QueryTypeProfile,
		Entity: models.EntityRequest{
			Type:               models.EntityTypePort,
			Identifier:         identifier,
			ResolutionStrategy: models.ResolutionFuzzy,
		},
		Metric:         "港口画像",
		TimeExpression: timeExpression,
	}

	// 执行查询
	response, err := h.queryService.ExecuteQuery(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get port profile",
			Message: err.Error(),
			Code:    "PROFILE_FAILED",
		})
		return
	}

	if response.Status == "error" {
		if response.Error == "port not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "Port not found",
				Message: response.Error,
				Code:    "PORT_NOT_FOUND",
			})
			return
		}
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Profile query failed",
			Message: response.Error,
			Code:    "PROFILE_QUERY_FAILED",
		})
		return
	}

	c.JSON(http.StatusOK, response.Data)
}

// GetRealtimeData 获取实时数据
// @Summary 获取实时数据
// @Description 获取指定实体的实时状态数据
// @Tags Realtime
// @Produce json
// @Param entity_type path string true "实体类型" Enums(Ship,Port)
// @Param entity_id path string true "实体标识符"
// @Success 200 {object} models.RealtimeData
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/realtime/{entity_type}/{entity_id} [get]
func (h *UnifiedHandler) GetRealtimeData(c *gin.Context) {
	entityType := c.Param("entity_type")
	entityID := c.Param("entity_id")

	if entityType == "" || entityID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing parameters",
			Message: "Both entity_type and entity_id are required",
			Code:    "MISSING_PARAMETER",
		})
		return
	}

	// 目前主要支持船舶实时数据
	if entityType != "Ship" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Unsupported entity type",
			Message: "Currently only Ship realtime data is supported",
			Code:    "UNSUPPORTED_ENTITY_TYPE",
		})
		return
	}

	// 获取船舶实时数据
	realtimeData, err := h.queryService.GetShipRealtimeData(c.Request.Context(), entityID)
	if err != nil {
		if err.Error() == "ship not found" {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "Ship not found",
				Message: err.Error(),
				Code:    "SHIP_NOT_FOUND",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get realtime data",
			Message: err.Error(),
			Code:    "REALTIME_FAILED",
		})
		return
	}

	c.JSON(http.StatusOK, realtimeData)
}

// GetMetadata 获取元数据
// @Summary 获取元数据
// @Description 获取系统元数据信息，如实体类型、指标列表等
// @Tags Metadata
// @Produce json
// @Param type path string true "元数据类型" Enums(entities,metrics,cargo_types,ship_types)
// @Success 200 {object} models.MetadataResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/metadata/{type} [get]
func (h *UnifiedHandler) GetMetadata(c *gin.Context) {
	metadataType := c.Param("type")

	if metadataType == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing metadata type",
			Message: "Metadata type is required",
			Code:    "MISSING_PARAMETER",
		})
		return
	}

	var data interface{}

	switch metadataType {
	case "entities":
		data = map[string]interface{}{
			"supported_entities": []string{"Ship", "Port", "Province", "Basin", "ShippingRoute", "CargoType"},
			"description":        "Supported entity types in the system",
		}
	case "metrics":
		// 使用统一的指标映射系统
		data = h.buildMetricsMetadata()
	case "cargo_types":
		data = map[string]interface{}{
			"categories": []string{"干散货类", "散装液货危险品", "集装箱类", "其他"},
			"sub_types": []string{
				"煤炭及制品", "石油、天然气及制品", "原油", "金属矿石", "钢铁", "矿物性建筑材料", "水泥", "木材",
				"非金属矿石", "磷矿", "化学肥料及农药", "盐",
				"粮食", "机械、设备、电器", "化工原料及制品", "有色金属",
				"轻工、医药产品", "日用工业品", "农林牧渔业产品", "棉花",
			},
		}
	case "ship_types":
		data = map[string]interface{}{
			"categories": []string{"客船类", "干散货", "集装箱", "滚装运输船", "危化品", "其他"},
			"sub_types": []string{
				"客货船", "普通客船", "客渡船", "车客渡船", "旅游客船", "高速客船", "客驳船", "滚装客船", "客箱船", "火车渡船（客）", "地效翼船", "高速客滚船", "干货船", "杂货船",
				"散货船", "散装水泥运输船", "集装箱船", "滚装船", "多用途船", "木材船", "水产品运输船", "重大件运输船", "驳船", "汽车渡船", "挂桨机船", "冷藏船", "火车渡船",
				"矿 / 散 / 油船", "半潜船", "油船", "散装化学品船", "散装化学品船 / 油船", "液化气船", "散装沥青船", "油驳", "一般液货船", "工程船", "测量船", "采沙船", "挖泥船",
				"疏浚船", "打捞船", "打桩船", "起重船", "搅拌船", "布缆船", "钻井船", "打桩起重船", "吹泥船", "起重驳", "工作船", "破冰船", "航标船", "油污水处理船", "供给船",
				"垃圾处理船", "拖船", "推轮", "交通艇", "引航船", "救助船", "浮船坞", "公务船", "摩托艇", "帆船", "趸船", "游艇", "特种用途船", "水上平台", "水下观光船",
				"科学调查船", "勘探船", "渔船", "其他",
			},
			"description": "船舶类型分类，基于船舶用途和载货类型",
		}
	default:
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Unsupported metadata type",
			Message: "Supported types: entities, metrics, cargo_types, ship_types",
			Code:    "UNSUPPORTED_METADATA_TYPE",
		})
		return
	}

	response := map[string]interface{}{
		"metadata_type": metadataType,
		"data":          data,
		"status":        "success",
	}

	// 打印格式化的JSON响应结果
	if jsonBytes, err := json.MarshalIndent(response, "", "  "); err == nil {
		log.Printf("[GetMetadata] Response JSON for type '%s':\n%s", metadataType, string(jsonBytes))
	} else {
		log.Printf("[GetMetadata] Failed to marshal response to JSON: %v", err)
	}

	c.JSON(http.StatusOK, response)
}

// buildMetricsMetadata 构建指标元数据（使用统一指标映射系统）
func (h *UnifiedHandler) buildMetricsMetadata() map[string]interface{} {
	result := make(map[string]interface{})

	// 支持的实体类型
	entityTypes := []models.EntityType{
		models.EntityTypeShip,
		models.EntityTypePort,
		models.EntityTypeShippingRoute,
	}

	for _, entityType := range entityTypes {
		// 获取该实体类型的所有指标
		metricInfos, err := h.metricService.GetEntityMetricsInfo(entityType)
		if err != nil {
			log.Printf("[buildMetricsMetadata] Failed to get metrics for entity type %s: %v", entityType, err)
			continue
		}

		// 转换为元数据格式
		var metrics []map[string]interface{}
		for _, metricInfo := range metricInfos {
			metric := map[string]interface{}{
				"name":         metricInfo.ChineseName,
				"english_name": metricInfo.EnglishName,
				"unit":         metricInfo.Unit,
				"description":  metricInfo.Description,
				"category":     metricInfo.Category,
				"db_field":     metricInfo.DBField,
				"aliases":      metricInfo.Aliases,
			}
			metrics = append(metrics, metric)
		}

		result[string(entityType)] = metrics
	}

	// 添加分类信息
	categories := h.metricService.GetAllCategories()
	result["categories"] = categories

	// 添加统计信息
	result["statistics"] = map[string]interface{}{
		"total_metrics":      len(h.getAllMetrics()),
		"total_categories":   len(categories),
		"supported_entities": len(entityTypes),
	}

	return result
}

// getAllMetrics 获取所有指标（用于统计）
func (h *UnifiedHandler) getAllMetrics() []*services.MetricInfo {
	var allMetrics []*services.MetricInfo

	entityTypes := []models.EntityType{
		models.EntityTypeShip,
		models.EntityTypePort,
		models.EntityTypeShippingRoute,
	}

	for _, entityType := range entityTypes {
		if metrics, err := h.metricService.GetEntityMetricsInfo(entityType); err == nil {
			allMetrics = append(allMetrics, metrics...)
		}
	}

	return allMetrics
}

// HealthCheck 健康检查接口
// @Summary 健康检查
// @Description 检查API服务状态
// @Tags System
// @Produce json
// @Success 200 {object} HealthResponse
// @Router /api/health [get]
func (h *UnifiedHandler) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, HealthResponse{
		Status:  "healthy",
		Version: "1.0.0",
		Message: "NeoAPI Unified Architecture is running",
	})
}

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

// HealthResponse 健康检查响应结构
type HealthResponse struct {
	Status  string `json:"status"`
	Version string `json:"version"`
	Message string `json:"message"`
}
