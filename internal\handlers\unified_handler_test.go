package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"neoapi/internal/models"
	"neoapi/internal/router"
	"neoapi/internal/services"
)

func TestUnifiedHandler_HealthCheck(t *testing.T) {
	// 设置测试路由
	gin.SetMode(gin.TestMode)
	mockDriver := &services.MockDriver{}
	r := router.SetupTestRouter(mockDriver)

	// 创建测试请求
	req, _ := http.NewRequest("GET", "/api/health", nil)
	w := httptest.NewRecorder()

	// 执行请求
	r.ServeHTTP(w, req)

	// 检查响应
	if w.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, w.Code)
	}

	var response HealthResponse
	if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
		t.Errorf("Failed to unmarshal response: %v", err)
	}

	if response.Status != "healthy" {
		t.<PERSON><PERSON><PERSON>("Expected status 'healthy', got '%s'", response.Status)
	}

	if response.Version != "3.1.0" {
		t.Errorf("Expected version '3.1.0', got '%s'", response.Version)
	}
}

func TestUnifiedHandler_UnifiedQuery(t *testing.T) {
	gin.SetMode(gin.TestMode)
	mockDriver := &services.MockDriver{}
	r := router.SetupTestRouter(mockDriver)

	tests := []struct {
		name           string
		request        models.UnifiedQueryRequest
		expectedStatus int
	}{
		{
			name: "有效的船舶画像查询",
			request: models.UnifiedQueryRequest{
				QueryType: models.QueryTypeProfile,
				Entity: models.EntityRequest{
					Type:               models.EntityTypeShip,
					Identifier:         "测试船舶",
					ResolutionStrategy: models.ResolutionFuzzy,
				},
				Metric:         "船舶画像",
				TimeExpression: "R1M",
				Options: models.QueryOptions{
					DetailLevel: "basic",
				},
			},
			expectedStatus: http.StatusOK,
		},
		{
			name: "无效的请求格式",
			request: models.UnifiedQueryRequest{
				// 缺少必需字段
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "无效的时间表达式",
			request: models.UnifiedQueryRequest{
				QueryType: models.QueryTypePoint,
				Entity: models.EntityRequest{
					Type:       models.EntityTypeShip,
					Identifier: "测试船舶",
				},
				Metric:         "货运量",
				TimeExpression: "INVALID_TIME",
			},
			expectedStatus: http.StatusOK, // 服务会返回错误状态，但HTTP状态码是200
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 序列化请求
			requestBody, err := json.Marshal(tt.request)
			if err != nil {
				t.Fatalf("Failed to marshal request: %v", err)
			}

			// 创建测试请求
			req, _ := http.NewRequest("POST", "/api/v3/query", bytes.NewBuffer(requestBody))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 检查响应状态码
			if w.Code != tt.expectedStatus {
				t.Errorf("Expected status code %d, got %d", tt.expectedStatus, w.Code)
			}

			// 如果期望成功，检查响应格式
			if tt.expectedStatus == http.StatusOK {
				var response models.UnifiedQueryResponse
				if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
				}

				// 检查响应状态
				if response.Status != "success" && response.Status != "error" {
					t.Errorf("Invalid response status: %s", response.Status)
				}

				// 检查查询元数据
				if response.Status == "success" && response.QueryMeta.QueryType != tt.request.QueryType {
					t.Errorf("Query type mismatch: got %s, want %s", 
						response.QueryMeta.QueryType, tt.request.QueryType)
				}
			}
		})
	}
}

func TestUnifiedHandler_SearchEntities(t *testing.T) {
	gin.SetMode(gin.TestMode)
	mockDriver := &services.MockDriver{}
	r := router.SetupTestRouter(mockDriver)

	tests := []struct {
		name           string
		query          string
		entityType     string
		expectedStatus int
	}{
		{
			name:           "有效的搜索查询",
			query:          "测试",
			entityType:     "Ship",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "缺少查询参数",
			query:          "",
			entityType:     "",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "只有查询关键词",
			query:          "船舶",
			entityType:     "",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 构建URL
			url := "/api/v3/search"
			if tt.query != "" {
				url += "?q=" + tt.query
				if tt.entityType != "" {
					url += "&type=" + tt.entityType
				}
			}

			// 创建测试请求
			req, _ := http.NewRequest("GET", url, nil)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 检查响应状态码
			if w.Code != tt.expectedStatus {
				t.Errorf("Expected status code %d, got %d", tt.expectedStatus, w.Code)
			}

			// 如果期望成功，检查响应格式
			if tt.expectedStatus == http.StatusOK {
				var response services.SearchResponse
				if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
					t.Errorf("Failed to unmarshal response: %v", err)
				}

				if response.Query != tt.query {
					t.Errorf("Query mismatch: got %s, want %s", response.Query, tt.query)
				}

				if response.Results == nil {
					t.Errorf("Results should not be nil")
				}
			}
		})
	}
}

func TestUnifiedHandler_GetShipProfile(t *testing.T) {
	gin.SetMode(gin.TestMode)
	mockDriver := &services.MockDriver{}
	r := router.SetupTestRouter(mockDriver)

	tests := []struct {
		name           string
		identifier     string
		timeExpression string
		expectedStatus int
	}{
		{
			name:           "有效的船舶标识符",
			identifier:     "测试船舶",
			timeExpression: "R1M",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "使用默认时间表达式",
			identifier:     "测试船舶",
			timeExpression: "",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 构建URL
			url := "/api/v3/ships/" + tt.identifier + "/profile"
			if tt.timeExpression != "" {
				url += "?time_expression=" + tt.timeExpression
			}

			// 创建测试请求
			req, _ := http.NewRequest("GET", url, nil)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 检查响应状态码
			if w.Code != tt.expectedStatus {
				t.Errorf("Expected status code %d, got %d", tt.expectedStatus, w.Code)
			}
		})
	}
}

func TestUnifiedHandler_GetRouteProfile(t *testing.T) {
	gin.SetMode(gin.TestMode)
	mockDriver := &services.MockDriver{}
	r := router.SetupTestRouter(mockDriver)

	tests := []struct {
		name           string
		identifier     string
		timeExpression string
		expectedStatus int
	}{
		{
			name:           "有效的航线标识符",
			identifier:     "测试航线",
			timeExpression: "R1M",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "使用默认时间表达式",
			identifier:     "测试航线",
			timeExpression: "",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 构建URL
			url := "/api/v3/routes/" + tt.identifier + "/profile"
			if tt.timeExpression != "" {
				url += "?time_expression=" + tt.timeExpression
			}

			// 创建测试请求
			req, _ := http.NewRequest("GET", url, nil)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 检查响应状态码
			if w.Code != tt.expectedStatus {
				t.Errorf("Expected status code %d, got %d", tt.expectedStatus, w.Code)
			}
		})
	}
}

// 基准测试
func BenchmarkUnifiedHandler_HealthCheck(b *testing.B) {
	gin.SetMode(gin.TestMode)
	mockDriver := &services.MockDriver{}
	r := router.SetupTestRouter(mockDriver)

	req, _ := http.NewRequest("GET", "/api/health", nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)
	}
}

func BenchmarkUnifiedHandler_UnifiedQuery(b *testing.B) {
	gin.SetMode(gin.TestMode)
	mockDriver := &services.MockDriver{}
	r := router.SetupTestRouter(mockDriver)

	request := models.UnifiedQueryRequest{
		QueryType: models.QueryTypeProfile,
		Entity: models.EntityRequest{
			Type:       models.EntityTypeShip,
			Identifier: "测试船舶",
		},
		Metric:         "船舶画像",
		TimeExpression: "R1M",
	}

	requestBody, _ := json.Marshal(request)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req, _ := http.NewRequest("POST", "/api/v3/query", bytes.NewBuffer(requestBody))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)
	}
}
