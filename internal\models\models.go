﻿package models

import (
	"time"
)

// ============================================================================
// 核心实体模型 (基于数据库设计文档v4.0)
// ============================================================================

// Ship 船舶档案
type Ship struct {
	MMSI              string    `json:"mmsi" neo4j:"mmsi"`     // 主键
	ShipID            string    `json:"shipId" neo4j:"shipId"` // 备用键
	ShipNo            string    `json:"shipNo,omitempty" neo4j:"shipNo"`
	Name              string    `json:"name" neo4j:"name"` // 船名
	FirstRegNo        string    `json:"firstRegNo,omitempty" neo4j:"firstRegNo"`
	InspectNo         string    `json:"inspectNo,omitempty" neo4j:"inspectNo"`
	RegPortCode       string    `json:"regPortCode,omitempty" neo4j:"regPortCode"`
	RegPortName       string    `json:"regPortName,omitempty" neo4j:"regPortName"`
	RegPortProvince   string    `json:"regPortProvince,omitempty" neo4j:"regPortProvince"`
	Owner             string    `json:"owner,omitempty" neo4j:"owner"`
	Operator          string    `json:"operator,omitempty" neo4j:"operator"`
	NavAreaType       string    `json:"navAreaType,omitempty" neo4j:"navAreaType"` // 1=海船, 0=内河
	DWT               float64   `json:"dwt,omitempty" neo4j:"dwt"`                 // 载重吨
	GrossTon          float64   `json:"grossTon,omitempty" neo4j:"grossTon"`       // 总吨位
	OrgCode           string    `json:"orgCode,omitempty" neo4j:"orgCode"`
	BuiltDate         time.Time `json:"builtDate,omitempty" neo4j:"builtDate"` // 建成时间
	DetailInfo        string    `json:"detailInfo,omitempty" neo4j:"detailInfo"`
	RegNo             string    `json:"regNo,omitempty" neo4j:"regNo"`
	LastProfileUpdate time.Time `json:"lastProfileUpdate,omitempty" neo4j:"lastProfileUpdate"`
}

// ShipCategory 船舶大类
type ShipCategory struct {
	Name string `json:"name" neo4j:"name"` // 主键，如"集装箱"
}

// ShipType 船舶类型
type ShipType struct {
	SubCode string `json:"subCode" neo4j:"subCode"` // 主键，如"0205"
	SubName string `json:"subName" neo4j:"subName"` // 如"集装箱船"
	Code    string `json:"code,omitempty" neo4j:"code"`
	Name    string `json:"name,omitempty" neo4j:"name"` // 如"集装箱"
}

// Port 港口
type Port struct {
	Name   string `json:"name" neo4j:"name"` // 主键
	Prov   string `json:"prov,omitempty" neo4j:"prov"`
	SortNo int    `json:"sortNo,omitempty" neo4j:"sortNo"`
}

// Province 省份
type Province struct {
	Name string `json:"name" neo4j:"name"` // 主键
	Code string `json:"code,omitempty" neo4j:"code"`
}

// Basin 流域
type Basin struct {
	Name string `json:"name" neo4j:"name"` // 主键，如"长江"
}

// ShippingRoute 航线
type ShippingRoute struct {
	RouteID             string    `json:"routeId" neo4j:"routeId"`     // 主键
	RouteName           string    `json:"routeName" neo4j:"routeName"` // 航线名称
	RouteCode           string    `json:"routeCode,omitempty" neo4j:"routeCode"`
	OriginPortName      string    `json:"originPortName" neo4j:"originPortName"`             // 起点港口
	DestinationPortName string    `json:"destinationPortName" neo4j:"destinationPortName"`   // 终点港口
	DistanceKm          float64   `json:"distance_km,omitempty" neo4j:"distance_km"`         // 距离
	RouteType           string    `json:"routeType,omitempty" neo4j:"routeType"`             // 干线/支线/联络线
	NavigationLevel     string    `json:"navigationLevel,omitempty" neo4j:"navigationLevel"` // 通航等级
	IsActive            bool      `json:"isActive" neo4j:"isActive"`
	EstablishedDate     time.Time `json:"establishedDate,omitempty" neo4j:"establishedDate"`
	LastUpdated         time.Time `json:"lastUpdated,omitempty" neo4j:"lastUpdated"`
}

// CargoCategory 货物大类
type CargoCategory struct {
	Name string `json:"name" neo4j:"name"` // 如"干散货类"
}

// CargoType 货物类别
type CargoType struct {
	SubCode string `json:"subCode" neo4j:"subCode"` // 主键，如"0100"
	SubName string `json:"subName" neo4j:"subName"` // 如"煤炭及制品"
	Name    string `json:"name,omitempty" neo4j:"name"`
}

// Year 年份
type Year struct {
	Year int `json:"year" neo4j:"year"` // 主键
}

// YearMonth 年月
type YearMonth struct {
	YM          string `json:"ym" neo4j:"ym"`       // 主键，格式YYYYMM
	Year        int    `json:"year" neo4j:"year"`   // 冗余字段
	Month       int    `json:"month" neo4j:"month"` // 冗余字段
	DaysInMonth int    `json:"daysInMonth,omitempty" neo4j:"daysInMonth"`
}

// ============================================================================
// 统计节点模型
// ============================================================================

// ShipMonthStat 船舶月度统计
type ShipMonthStat struct {
	Name              string    `json:"name" neo4j:"name"`                                     // 船名
	OpRatio           float64   `json:"opRatio,omitempty" neo4j:"opRatio"`                     // 有效营运率
	Voyages           int       `json:"voyages,omitempty" neo4j:"voyages"`                     // 航次数
	LoadRatio         float64   `json:"loadRatio,omitempty" neo4j:"loadRatio"`                 // 负载率
	AnchorTimeDays    float64   `json:"anchorTime_day,omitempty" neo4j:"anchorTime_day"`       // 在港时间
	SailTimeDays      float64   `json:"sailTime_day,omitempty" neo4j:"sailTime_day"`           // 航行时间
	SailRatio         float64   `json:"sailRatio,omitempty" neo4j:"sailRatio"`                 // 航行率
	TurnoverTonKm     float64   `json:"turnover_tonkm,omitempty" neo4j:"turnover_tonkm"`       // 周转量
	LoadVoyages       int       `json:"loadVoyages,omitempty" neo4j:"loadVoyages"`             // 负载航次数
	DispatchLoadRatio float64   `json:"dispatchLoadRatio,omitempty" neo4j:"dispatchLoadRatio"` // 发船装载率
	DistanceLoadRatio float64   `json:"distanceLoadRatio,omitempty" neo4j:"distanceLoadRatio"` // 运距装载率
	CapacityTon       float64   `json:"capacity_ton,omitempty" neo4j:"capacity_ton"`           // 货运量
	LoadVoyRatio      float64   `json:"loadVoyRatio,omitempty" neo4j:"loadVoyRatio"`           // 负载航次率
	MileageKm         float64   `json:"mileage_km,omitempty" neo4j:"mileage_km"`               // 运距
	TonDayOutput      float64   `json:"tonDayOutput,omitempty" neo4j:"tonDayOutput"`           // 吨天产量
	TonShipOutput     float64   `json:"tonShipOutput,omitempty" neo4j:"tonShipOutput"`         // 吨船产量
	LastUpdated       time.Time `json:"lastUpdated,omitempty" neo4j:"lastUpdated"`
}

// ShipMonthCargoStat 船舶月度分货类统计
type ShipMonthCargoStat struct {
	Name        string    `json:"name" neo4j:"name"`           // 船名
	SubName     string    `json:"subName" neo4j:"subName"`     // 货物类型名称
	CargoTon    float64   `json:"cargo_ton" neo4j:"cargo_ton"` // 货运量
	LastUpdated time.Time `json:"lastUpdated,omitempty" neo4j:"lastUpdated"`
}

// ShipMonthLineStat 船舶月度分航线统计
type ShipMonthLineStat struct {
	Name          string    `json:"name" neo4j:"name"`                               // 船名
	CargoTon      float64   `json:"cargo_ton" neo4j:"cargo_ton"`                     // 货运量
	TurnoverTonKm float64   `json:"turnover_tonkm,omitempty" neo4j:"turnover_tonkm"` // 周转量
	MileageKm     float64   `json:"mileage_km,omitempty" neo4j:"mileage_km"`         // 里程
	VoyageCount   int       `json:"voyageCount,omitempty" neo4j:"voyageCount"`       // 航次数
	AvgLoadRatio  float64   `json:"avgLoadRatio,omitempty" neo4j:"avgLoadRatio"`     // 平均装载率
	LastUpdated   time.Time `json:"lastUpdated,omitempty" neo4j:"lastUpdated"`
}

// RouteMonthStat 航线月度统计
type RouteMonthStat struct {
	TotalShipCount     int       `json:"totalShipCount" neo4j:"totalShipCount"`                     // 运营船舶总数
	TotalVoyageCount   int       `json:"totalVoyageCount" neo4j:"totalVoyageCount"`                 // 总航次数
	TotalCargoTon      float64   `json:"totalCargo_ton" neo4j:"totalCargo_ton"`                     // 总货运量
	TotalTurnoverTonKm float64   `json:"totalTurnover_tonkm,omitempty" neo4j:"totalTurnover_tonkm"` // 总周转量
	AvgLoadRatio       float64   `json:"avgLoadRatio,omitempty" neo4j:"avgLoadRatio"`               // 平均装载率
	AvgVoyageTimeHours float64   `json:"avgVoyageTime_hours,omitempty" neo4j:"avgVoyageTime_hours"` // 平均航行时间
	UtilizationRate    float64   `json:"utilizationRate,omitempty" neo4j:"utilizationRate"`         // 航线利用率
	LastUpdated        time.Time `json:"lastUpdated,omitempty" neo4j:"lastUpdated"`
}

// RouteMonthCargoStat 航线月度分货类统计
type RouteMonthCargoStat struct {
	CargoTon             float64   `json:"cargo_ton" neo4j:"cargo_ton"`                                   // 货运量
	ShipCount            int       `json:"shipCount,omitempty" neo4j:"shipCount"`                         // 运输船舶数
	VoyageCount          int       `json:"voyageCount,omitempty" neo4j:"voyageCount"`                     // 航次数
	AvgCargoPerVoyageTon float64   `json:"avgCargoPerVoyage_ton,omitempty" neo4j:"avgCargoPerVoyage_ton"` // 平均每航次货运量
	LastUpdated          time.Time `json:"lastUpdated,omitempty" neo4j:"lastUpdated"`
}

// PortMonthStat 港口月度统计
type PortMonthStat struct {
	InShipCount    int       `json:"inShipCount" neo4j:"inShipCount"`                   // 进港艘次
	OutShipCount   int       `json:"outShipCount" neo4j:"outShipCount"`                 // 出港艘次
	InCargoTon     float64   `json:"inCargo_ton" neo4j:"inCargo_ton"`                   // 进港运量
	OutCargoTon    float64   `json:"outCargo_ton" neo4j:"outCargo_ton"`                 // 出港运量
	AnchorTimeDays float64   `json:"anchorTime_days,omitempty" neo4j:"anchorTime_days"` // 在港时间
	InLoadRatio    float64   `json:"inLoadRatio,omitempty" neo4j:"inLoadRatio"`         // 进港装载率
	OutLoadRatio   float64   `json:"outLoadRatio,omitempty" neo4j:"outLoadRatio"`       // 出港装载率
	LastUpdated    time.Time `json:"lastUpdated,omitempty" neo4j:"lastUpdated"`
}

// PortMonthCargoStat 港口月度分货类统计
type PortMonthCargoStat struct {
	InCargoTon  float64   `json:"inCargo_ton" neo4j:"inCargo_ton"`   // 进港货运量
	OutCargoTon float64   `json:"outCargo_ton" neo4j:"outCargo_ton"` // 出港货运量
	LastUpdated time.Time `json:"lastUpdated,omitempty" neo4j:"lastUpdated"`
}

// ShipRealtime 船舶实时状态
type ShipRealtime struct {
	// AIS物理状态
	Lat          float64   `json:"lat,omitempty" neo4j:"lat"`                   // 纬度
	Lon          float64   `json:"lon,omitempty" neo4j:"lon"`                   // 经度
	SOG          float64   `json:"sog,omitempty" neo4j:"sog"`                   // 对地航速
	COG          float64   `json:"cog,omitempty" neo4j:"cog"`                   // 对地航向
	NavStatus    string    `json:"navStatus,omitempty" neo4j:"navStatus"`       // 导航状态
	AISTimestamp time.Time `json:"aisTimestamp,omitempty" neo4j:"aisTimestamp"` // AIS时间戳
	Km           float64   `json:"km,omitempty" neo4j:"km"`                     // 航道里程
	PortAIS      string    `json:"portAis,omitempty" neo4j:"portAis"`           // AIS反向地理编码
	OrgName      string    `json:"orgName,omitempty" neo4j:"orgName"`           // 管辖海事机构

	// 报港业务状态
	PortStatus                string    `json:"portStatus,omitempty" neo4j:"portStatus"`                                 // 业务状态
	PortReport                string    `json:"portReport,omitempty" neo4j:"portReport"`                                 // 报告港口
	ReportTimeIn              time.Time `json:"reportTimeIn,omitempty" neo4j:"reportTimeIn"`                             // 进港报告时间
	ReportTimeOut             time.Time `json:"reportTimeOut,omitempty" neo4j:"reportTimeOut"`                           // 出港报告时间
	ActualCarryCapacityInTon  float64   `json:"actualCarryCapacityIn_ton,omitempty" neo4j:"actualCarryCapacityIn_ton"`   // 进港实载
	ActualCarryCapacityOutTon float64   `json:"actualCarryCapacityOut_ton,omitempty" neo4j:"actualCarryCapacityOut_ton"` // 出港实载
	PortCargoIn               string    `json:"portCargoIn_to,omitempty" neo4j:"portCargoIn_to"`                         // 进港装卸情况
	PortCargoOut              string    `json:"portCargoOut_ton,omitempty" neo4j:"portCargoOut_ton"`                     // 出港装卸情况

	// 元数据
	LastUpdated time.Time `json:"lastUpdated,omitempty" neo4j:"lastUpdated"` // 最后更新时间
}

// ============================================================================
// API请求和响应模型 (基于新API设计v3.1)
// ============================================================================

// QueryType 查询类型枚举
type QueryType string

const (
	QueryTypePoint   QueryType = "POINT"   // 单点查询
	QueryTypeProfile QueryType = "PROFILE" // 画像查询
	QueryTypeTrend   QueryType = "TREND"   // 趋势分析
	QueryTypeCompare QueryType = "COMPARE" // 对比分析
	QueryTypeRank    QueryType = "RANK"    // 排名分析 (已废弃，使用EntityQuery替代)
	QueryTypeCompose QueryType = "COMPOSE" // 构成分析
)

// ============================================================================
// 智能实体查询模型
// ============================================================================

// EntityQueryRequest 智能实体查询请求
type EntityQueryRequest struct {
	EntityType     EntityType    `json:"entity_type" binding:"required"`
	TimeExpression string        `json:"time_expression" binding:"required"`
	Filters        EntityFilters `json:"filters,omitempty"`
	Limit          int           `json:"limit,omitempty"`
}

// EntityFilters 实体过滤条件
type EntityFilters struct {
	// ============ 文本搜索 ============
	Name string `json:"name,omitempty"`

	// ============ 数值范围过滤 ============
	DwtMin       *float64 `json:"dwt_min,omitempty"`
	DwtMax       *float64 `json:"dwt_max,omitempty"`
	DistanceMin  *float64 `json:"distance_min,omitempty"`
	DistanceMax  *float64 `json:"distance_max,omitempty"`
	BuildYearMin *int     `json:"build_year_min,omitempty"`
	BuildYearMax *int     `json:"build_year_max,omitempty"`

	// ============ 分类属性过滤 ============
	ShipTypes  []string `json:"ship_types,omitempty"`
	RouteTypes []string `json:"route_types,omitempty"`
	CargoTypes []string `json:"cargo_types,omitempty"`
	Provinces  []string `json:"provinces,omitempty"`

	// ============ 地理位置过滤 ============
	OriginPorts      []string `json:"origin_ports,omitempty"`
	DestinationPorts []string `json:"destination_ports,omitempty"`
	ViaPorts         []string `json:"via_ports,omitempty"`

	// ============ 运营行为过滤 ============
	ActiveRoutes      []string `json:"active_routes,omitempty"`
	ServedPorts       []string `json:"served_ports,omitempty"`
	TransportedCargos []string `json:"transported_cargos,omitempty"`

	// ============ 性能阈值过滤 ============
	MinVoyages     *int     `json:"min_voyages,omitempty"`
	MaxVoyages     *int     `json:"max_voyages,omitempty"`
	MinCargoVolume *float64 `json:"min_cargo_volume,omitempty"`
	MaxCargoVolume *float64 `json:"max_cargo_volume,omitempty"`
	MinLoadRatio   *float64 `json:"min_load_ratio,omitempty"`
	MaxLoadRatio   *float64 `json:"max_load_ratio,omitempty"`
	MinShips       *int     `json:"min_ships,omitempty"`
	MaxShips       *int     `json:"max_ships,omitempty"`

	// ============ 实体特定字段 ============
	// Ship专用
	MMSI              string   `json:"mmsi,omitempty"`
	ShipID            string   `json:"ship_id,omitempty"`
	OwnerCompanies    []string `json:"owner_companies,omitempty"`
	OperatorCompanies []string `json:"operator_companies,omitempty"`
	HomePorts         []string `json:"home_ports,omitempty"`

	// Route专用
	RouteCodes       []string `json:"route_codes,omitempty"`
	NavigationLevels []string `json:"navigation_levels,omitempty"`

	// ============ 排除条件 ============
	ExcludeNames  []string `json:"exclude_names,omitempty"`
	ExcludeTypes  []string `json:"exclude_types,omitempty"`
	ExcludeRoutes []string `json:"exclude_routes,omitempty"`
	ExcludePorts  []string `json:"exclude_ports,omitempty"`
}

// EntityQueryResponse 智能实体查询响应
type EntityQueryResponse struct {
	EntityType      EntityType     `json:"entity_type"`
	TimePeriod      string         `json:"time_period"`
	Results         []EntityResult `json:"results"`
	TotalFound      int            `json:"total_found"`
	Returned        int            `json:"returned"`
	ExecutionTimeMs int64          `json:"execution_time_ms"`
}

// EntityResult 实体查询结果
type EntityResult struct {
	// 基础信息
	Name       string     `json:"name"`
	Identifier string     `json:"identifier,omitempty"` // MMSI, RouteID等
	EntityType EntityType `json:"entity_type"`

	// 静态属性
	Properties map[string]interface{} `json:"properties,omitempty"`

	// 运营指标
	Metrics EntityMetrics `json:"metrics"`

	// 关联数据
	Routes     []string `json:"routes,omitempty"`
	CargoTypes []string `json:"cargo_types,omitempty"`
	Ports      []string `json:"ports,omitempty"`
}

// EntityMetrics 实体运营指标
type EntityMetrics struct {
	CargoVolume float64 `json:"cargo_volume,omitempty"`
	VoyageCount int     `json:"voyage_count,omitempty"`
	LoadRatio   float64 `json:"load_ratio,omitempty"`
	ShipCount   int     `json:"ship_count,omitempty"`
	TotalCargo  float64 `json:"total_cargo,omitempty"`
}

// EntityType 实体类型枚举
type EntityType string

const (
	EntityTypeShip          EntityType = "Ship"
	EntityTypePort          EntityType = "Port"
	EntityTypeProvince      EntityType = "Province"
	EntityTypeBasin         EntityType = "Basin"
	EntityTypeShippingRoute EntityType = "ShippingRoute"
	EntityTypeCargoType     EntityType = "CargoType"
)

// ResolutionStrategy 实体解析策略
type ResolutionStrategy string

const (
	ResolutionExact ResolutionStrategy = "exact" // 精确匹配
	ResolutionFuzzy ResolutionStrategy = "fuzzy" // 模糊匹配
	ResolutionMulti ResolutionStrategy = "multi" // 多实体匹配
)

// EntityRequest 实体请求
type EntityRequest struct {
	Type               EntityType         `json:"type" binding:"required"`
	Identifier         interface{}        `json:"identifier" binding:"required"` // string或[]string
	ResolutionStrategy ResolutionStrategy `json:"resolution_strategy,omitempty"`
}

// UnifiedQueryRequest 统一查询请求
type UnifiedQueryRequest struct {
	QueryType      QueryType     `json:"query_type" binding:"required"`
	Entity         EntityRequest `json:"entity" binding:"required"`
	Metric         string        `json:"metric"` // 移除required，允许空值降级处理
	TimeExpression string        `json:"time_expression" binding:"required"`
	Filters        interface{}   `json:"filters,omitempty"`
	Limit          int           `json:"limit,omitempty"` // 仅用于RANK查询，默认10，最大100
}

// ============================================================================
// 响应模型
// ============================================================================

// EntityInfo 实体信息
type EntityInfo struct {
	Type             EntityType `json:"type"`
	Name             string     `json:"name,omitempty"`
	MMSI             string     `json:"mmsi,omitempty"`
	ShipID           string     `json:"shipId,omitempty"`
	RouteID          string     `json:"routeId,omitempty"`
	RouteName        string     `json:"routeName,omitempty"`
	OriginPort       string     `json:"originPort,omitempty"`
	DestinationPort  string     `json:"destinationPort,omitempty"`
	DistanceKm       float64    `json:"distance_km,omitempty"`
	ResolutionMethod string     `json:"resolution_method,omitempty"`
}

// MetricInfo 指标信息
type MetricInfo struct {
	DisplayName string `json:"display_name"`
	DBField     string `json:"db_field"`
	Unit        string `json:"unit,omitempty"`
}

// TimeInfo 时间信息
type TimeInfo struct {
	Expression      string   `json:"expression"`
	ResolvedPeriods []string `json:"resolved_periods"`
	Granularity     string   `json:"granularity"`
}

// QueryMeta 查询元数据
type QueryMeta struct {
	QueryType QueryType  `json:"query_type"`
	Entity    EntityInfo `json:"entity"`
	Metric    MetricInfo `json:"metric"`
	Time      TimeInfo   `json:"time,omitempty"`
	TimeRange string     `json:"time_range,omitempty"`
}

// PerformanceInfo 性能信息
type PerformanceInfo struct {
	QueryTimeMs int  `json:"query_time_ms"`
	CacheHit    bool `json:"cache_hit,omitempty"`
}

// ContextInfo 上下文信息
type ContextInfo struct {
	TotalDays     int             `json:"total_days,omitempty"`
	OperatingDays float64         `json:"operating_days,omitempty"`
	TotalShips    int             `json:"total_ships,omitempty"`
	TotalVoyages  int             `json:"total_voyages,omitempty"`
	AvgLoadRatio  float64         `json:"avg_load_ratio,omitempty"`
	DataSource    string          `json:"data_source,omitempty"`
	Status        string          `json:"status,omitempty"`
	Message       string          `json:"message,omitempty"`
	Fallback      *FallbackResult `json:"fallback,omitempty"`
}

// UnifiedQueryResponse 统一查询响应
type UnifiedQueryResponse struct {
	Entity     interface{} `json:"entity,omitempty"`      // 单个实体名称
	Entities   []string    `json:"entities,omitempty"`    // 多个实体列表（COMPARE查询）
	EntityType string      `json:"entity_type,omitempty"` // 实体类型（RANK查询）
	Metric     string      `json:"metric"`
	Period     string      `json:"period"`
	Data       interface{} `json:"data"`
	Status     string      `json:"status"`
	Error      string      `json:"error,omitempty"`
}

// PointQueryData 单点查询数据
type PointQueryData struct {
	Value interface{} `json:"value"`
	Unit  string      `json:"unit,omitempty"`
}

// TrendDataPoint 趋势数据点
type TrendDataPoint struct {
	Period string      `json:"period"`
	Value  interface{} `json:"value"`
	Unit   string      `json:"unit,omitempty"`
}

// TrendAnalysis 趋势分析
type TrendAnalysis struct {
	TrendDirection string  `json:"trend_direction"` // 上升/下降/平稳
	GrowthRate     float64 `json:"growth_rate"`
	Volatility     string  `json:"volatility"` // 低/中等/高
}

// TrendQueryData 趋势查询数据
type TrendQueryData struct {
	Trend []TrendDataPoint `json:"trend"`
	Unit  string           `json:"unit,omitempty"`
}

// ComparisonDataPoint 对比数据点
type ComparisonDataPoint struct {
	Entity  string             `json:"entity"`
	Value   interface{}        `json:"value"`             // 聚合值（单个时间点或时间范围的总和/平均值）
	Details []TrendDataPoint   `json:"details,omitempty"` // 时间范围内的明细数据
	Summary *ComparisonSummary `json:"summary,omitempty"` // 时间范围的统计摘要
}

// ComparisonSummary 对比统计摘要
type ComparisonSummary struct {
	Total   float64 `json:"total"`   // 总和
	Average float64 `json:"average"` // 平均值
	Max     float64 `json:"max"`     // 最大值
	Min     float64 `json:"min"`     // 最小值
	Count   int     `json:"count"`   // 数据点数量
}

// EntityProfileData 实体完整属性数据
type EntityProfileData struct {
	BasicInfo        map[string]interface{} `json:"basic_info"`
	AvailableMetrics map[string]interface{} `json:"available_metrics"`
	TimeRange        string                 `json:"time_range"`
	LastUpdated      string                 `json:"last_updated,omitempty"`
	DataSources      []string               `json:"data_sources,omitempty"`
}

// FallbackResult 统一降级处理结果
type FallbackResult struct {
	Value           interface{}            `json:"value"`
	Unit            string                 `json:"unit,omitempty"`
	Status          string                 `json:"status"`
	Message         string                 `json:"message"`
	FallbackType    string                 `json:"fallback_type,omitempty"`
	AlternativeInfo map[string]interface{} `json:"alternative_info,omitempty"`
}

// CompareQueryData 对比查询数据
type CompareQueryData struct {
	Comparison []ComparisonDataPoint `json:"comparison"`
	Unit       string                `json:"unit,omitempty"`
}

// RankingDataPoint 排名数据点
type RankingDataPoint struct {
	Rank       int         `json:"rank"`
	EntityName string      `json:"entity_name"`
	Value      interface{} `json:"value"`
	Unit       string      `json:"unit,omitempty"`
}

// RankQueryData 排名查询数据
type RankQueryData struct {
	Ranking []RankingDataPoint `json:"ranking"`
	Unit    string             `json:"unit,omitempty"`
}

// CompositionDataPoint 构成数据点
type CompositionDataPoint struct {
	Name       string  `json:"name"`
	Value      float64 `json:"value"`
	Percentage float64 `json:"percentage"`
	Unit       string  `json:"unit,omitempty"`
}

// ComposeQueryData 构成查询数据
type ComposeQueryData struct {
	Composition []CompositionDataPoint `json:"composition"`
	Total       float64                `json:"total,omitempty"`
	Unit        string                 `json:"unit,omitempty"`
}

// ============================================================================
// 船舶画像专用模型
// ============================================================================

// ShipBasicInfo 船舶基本信息
type ShipBasicInfo struct {
	MMSI            string    `json:"mmsi"`
	Name            string    `json:"name"`
	Owner           string    `json:"owner,omitempty"`
	Operator        string    `json:"operator,omitempty"`
	DWT             float64   `json:"dwt,omitempty"`
	GrossTon        float64   `json:"grossTon,omitempty"`
	BuiltDate       time.Time `json:"builtDate,omitempty"`
	RegPortName     string    `json:"regPortName,omitempty"`
	RegPortProvince string    `json:"regPortProvince,omitempty"`
	NavAreaType     string    `json:"navAreaType,omitempty"`
}

// ShipTypeInfo 船舶类型信息
type ShipTypeInfo struct {
	Category string `json:"category"`
	SubType  string `json:"subType"`
	SubCode  string `json:"subCode"`
}

// ShipRecentStats 船舶最近统计
type ShipRecentStats struct {
	LatestMonth   string  `json:"latest_month"`
	OpRatio       float64 `json:"opRatio,omitempty"`
	Voyages       int     `json:"voyages,omitempty"`
	LoadRatio     float64 `json:"loadRatio,omitempty"`
	TurnoverTonKm float64 `json:"turnover_tonkm,omitempty"`
	CapacityTon   float64 `json:"capacity_ton,omitempty"`
}

// HistoricalPerformance 历史表现
type HistoricalPerformance struct {
	Period           string             `json:"period"`
	OpRatio          float64            `json:"opRatio,omitempty"`
	Voyages          int                `json:"voyages,omitempty"`
	LoadRatio        float64            `json:"loadRatio,omitempty"`
	TurnoverTonkm    float64            `json:"turnover_tonkm,omitempty"`
	CapacityTon      float64            `json:"capacity_ton,omitempty"`
	OpTimeMin        int                `json:"opTime_min,omitempty"`
	AnchorTimeMin    int                `json:"anchorTime_min,omitempty"`
	AvgSpeedKmh      float64            `json:"avgSpeed_kmh,omitempty"`
	FuelConsumption  float64            `json:"fuelConsumption_ton,omitempty"`
	CargoComposition []CargoComposition `json:"cargo_composition,omitempty"`
	RouteBreakdown   []RouteBreakdown   `json:"route_breakdown,omitempty"`
}

// RouteBreakdown 航线分析
type RouteBreakdown struct {
	Route        string  `json:"route"`
	Voyages      int     `json:"voyages"`
	CargoTon     float64 `json:"cargo_ton"`
	AvgLoadRatio float64 `json:"avg_load_ratio"`
}

// CargoComposition 货物构成
type CargoComposition struct {
	CargoType    string  `json:"cargo_type"`
	TotalTon     float64 `json:"total_ton"`
	Percentage   float64 `json:"percentage"`
	MonthsActive int     `json:"months_active"`
}

// RouteAnalysis 航线分析
type RouteAnalysis struct {
	Route           string  `json:"route"`
	Frequency       int     `json:"frequency"`
	TotalCargo      float64 `json:"total_cargo"`
	AvgCargoPerTrip float64 `json:"avg_cargo_per_trip"`
}

// ShipRelationships 船舶关系
type ShipRelationships struct {
	HomePort          string   `json:"home_port"`
	FrequentPorts     []string `json:"frequent_ports"`
	OperatingProvince string   `json:"operating_province"`
	Basin             string   `json:"basin"`
}

// ShipProfileSummary 船舶画像摘要
type ShipProfileSummary struct {
	TotalMonthsData    int      `json:"total_months_data"`
	AvgMonthlyCapacity float64  `json:"avg_monthly_capacity"`
	PrimaryCargoTypes  []string `json:"primary_cargo_types"`
	MainRoutes         []string `json:"main_routes"`
	PerformanceRating  string   `json:"performance_rating"`
}

// ShipProfileData 船舶画像数据
type ShipProfileData struct {
	BasicInfo             ShipBasicInfo           `json:"basic_info"`
	ShipType              ShipTypeInfo            `json:"ship_type"`
	RecentStats           ShipRecentStats         `json:"recent_stats"`
	HistoricalPerformance []HistoricalPerformance `json:"historical_performance,omitempty"`
	CargoComposition      []CargoComposition      `json:"cargo_composition,omitempty"`
	RouteAnalysis         []RouteAnalysis         `json:"route_analysis,omitempty"`
	Relationships         ShipRelationships       `json:"relationships,omitempty"`
	RealtimeStatus        *ShipRealtime           `json:"realtime_status,omitempty"`
	Summary               ShipProfileSummary      `json:"summary"`
}

// ============================================================================
// 港口画像专用模型
// ============================================================================

// PortBasicInfo 港口基本信息
type PortBasicInfo struct {
	Name     string `json:"name"`
	Province string `json:"province,omitempty"`
	SortNo   int    `json:"sortNo,omitempty"`
}

// PortRecentStats 港口最近统计
type PortRecentStats struct {
	LatestMonth     string  `json:"latest_month"`
	TotalThroughput float64 `json:"total_throughput,omitempty"`
	InboundShips    int     `json:"inbound_ships,omitempty"`
	OutboundShips   int     `json:"outbound_ships,omitempty"`
	InboundCargo    float64 `json:"inbound_cargo,omitempty"`
	OutboundCargo   float64 `json:"outbound_cargo,omitempty"`
}

// PortProfileData 港口画像数据
type PortProfileData struct {
	BasicInfo             PortBasicInfo           `json:"basic_info"`
	RecentStats           PortRecentStats         `json:"recent_stats"`
	HistoricalPerformance []HistoricalPerformance `json:"historical_performance,omitempty"`
	CargoComposition      []CargoComposition      `json:"cargo_composition,omitempty"`
	ShipAnalysis          []ShipAnalysis          `json:"ship_analysis,omitempty"`
	Summary               PortProfileSummary      `json:"summary"`
}

// ShipAnalysis 船舶分析
type ShipAnalysis struct {
	ShipType   string  `json:"ship_type"`
	Count      int     `json:"count"`
	TotalCargo float64 `json:"total_cargo"`
	AvgDWT     float64 `json:"avg_dwt"`
}

// PortProfileSummary 港口画像摘要
type PortProfileSummary struct {
	TotalMonthsData      int      `json:"total_months_data"`
	AvgMonthlyThroughput float64  `json:"avg_monthly_throughput"`
	PrimaryCargoTypes    []string `json:"primary_cargo_types"`
	MainShipTypes        []string `json:"main_ship_types"`
	PerformanceRating    string   `json:"performance_rating"`
}

// ============================================================================
// 实时数据模型
// ============================================================================

// RealtimeData 实时数据响应
type RealtimeData struct {
	Entity       EntityInfo             `json:"entity"`
	RealtimeData map[string]interface{} `json:"realtime_data"`
	Status       string                 `json:"status"`
	Timestamp    time.Time              `json:"timestamp,omitempty"`
}

// MetadataResponse 元数据响应
type MetadataResponse struct {
	MetadataType string      `json:"metadata_type"`
	Data         interface{} `json:"data"`
	Status       string      `json:"status"`
}
