package router

import (
	"time"

	"neoapi/internal/handlers"
	"neoapi/internal/middleware"

	"github.com/gin-gonic/gin"
	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
)

// SetupRouter 设置路由
func SetupRouter(driver neo4j.DriverWithContext) *gin.Engine {
	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)

	// 创建Gin引擎
	r := gin.New()

	// 添加全局中间件
	r.Use(middleware.Recovery())
	r.Use(middleware.Logger())
	r.Use(middleware.CORS())
	r.Use(middleware.Security())
	r.Use(middleware.RequestID())
	r.Use(middleware.RequestValidator())
	r.Use(middleware.RateLimiter())
	r.Use(middleware.Timeout(30 * time.Second)) // 30秒超时
	r.Use(middleware.ErrorHandler())

	// 创建处理器
	unifiedHandler := handlers.NewUnifiedHandler(driver)
	metricHandler := handlers.NewMetricHandler()

	// 健康检查接口
	r.GET("/api/health", unifiedHandler.HealthCheck)

	// 统一API路由组 - 分层架构设计
	api := r.Group("/api")
	{
		// 核心查询接口 (POINT, PROFILE, TREND)
		core := api.Group("/core")
		{
			core.POST("/query", unifiedHandler.CoreQuery)
		}

		// 分析查询接口 (COMPARE, RANK, COMPOSE)
		analytics := api.Group("/analytics")
		{
			analytics.POST("/query", unifiedHandler.AnalyticsQuery)
		}

		// 统一查询接口 (支持所有查询类型)
		api.POST("/query", unifiedHandler.UnifiedQuery)

		// 实体搜索接口
		search := api.Group("/search")
		{
			search.POST("/entities", unifiedHandler.SearchEntities)
			search.GET("/entities", unifiedHandler.SearchEntitiesLegacy) // 兼容GET方法
		}

		// 智能实体查询接口 (v3.1)
		v31 := api.Group("/v3.1")
		{
			v31.POST("/query/entities", unifiedHandler.QueryEntities)
		}

		// 实时数据接口
		realtime := api.Group("/realtime")
		{
			realtime.GET("/:entity_type/:entity_id", unifiedHandler.GetRealtimeData)
		}

		// 元数据服务接口
		metadata := api.Group("/metadata")
		{
			metadata.GET("/:type", unifiedHandler.GetMetadata)
		}

		// 指标管理接口
		metrics := api.Group("/metrics")
		{
			metrics.GET("", metricHandler.GetMetrics)                  // 获取指标列表
			metrics.GET("/:name", metricHandler.GetMetricInfo)         // 获取单个指标信息
			metrics.GET("/categories", metricHandler.GetCategories)    // 获取指标分类
			metrics.GET("/suggestions", metricHandler.GetSuggestions)  // 获取指标建议
			metrics.GET("/entity-types", metricHandler.GetEntityTypes) // 获取实体类型
			metrics.POST("/validate", metricHandler.ValidateMetric)    // 验证指标
			metrics.POST("/refresh", metricHandler.RefreshMetrics)     // 刷新指标注册表
		}

		// 实体画像接口
		profiles := api.Group("/profiles")
		{
			// 船舶画像
			profiles.GET("/ships/:identifier", unifiedHandler.GetShipProfile)

			// 港口画像
			profiles.GET("/ports/:identifier", unifiedHandler.GetPortProfile)

			// 航线画像
			profiles.GET("/routes/:identifier", unifiedHandler.GetRouteProfile)
		}
	}

	// 文档和静态文件
	r.Static("/docs", "./docs")
	r.GET("/", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"message": "Welcome to NeoAPI - Unified Architecture",
			"version": "1.0.0",
			"docs":    "/docs",
			"health":  "/api/health",
			"endpoints": gin.H{
				"core_query":      "/api/core/query",
				"analytics_query": "/api/analytics/query",
				"unified_query":   "/api/query",
				"search_entities": "/api/search/entities",
				"realtime_data":   "/api/realtime/{entity_type}/{entity_id}",
				"metadata":        "/api/metadata/{type}",
				"ship_profile":    "/api/profiles/ships/{identifier}",
				"port_profile":    "/api/profiles/ports/{identifier}",
				"route_profile":   "/api/profiles/routes/{identifier}",
			},
			"description": "Unified API with layered architecture for maritime data analytics",
		})
	})

	return r
}

// SetupTestRouter 设置测试路由（用于单元测试）
func SetupTestRouter(driver neo4j.DriverWithContext) *gin.Engine {
	gin.SetMode(gin.TestMode)

	r := gin.New()

	// 只添加必要的中间件
	r.Use(middleware.Recovery())
	r.Use(middleware.CORS())
	r.Use(middleware.RequestValidator())
	r.Use(middleware.ErrorHandler())

	// 创建处理器
	unifiedHandler := handlers.NewUnifiedHandler(driver)

	// 健康检查
	r.GET("/api/health", unifiedHandler.HealthCheck)

	// 统一API路由
	api := r.Group("/api")
	{
		api.POST("/query", unifiedHandler.UnifiedQuery)
		api.POST("/core/query", unifiedHandler.CoreQuery)
		api.POST("/analytics/query", unifiedHandler.AnalyticsQuery)
		api.POST("/search/entities", unifiedHandler.SearchEntities)
		api.GET("/search/entities", unifiedHandler.SearchEntitiesLegacy)
		api.POST("/v3.1/query/entities", unifiedHandler.QueryEntities) // 新的智能实体查询
		api.GET("/profiles/ships/:identifier", unifiedHandler.GetShipProfile)
		api.GET("/profiles/ports/:identifier", unifiedHandler.GetPortProfile)
		api.GET("/profiles/routes/:identifier", unifiedHandler.GetRouteProfile)
		api.GET("/realtime/:entity_type/:entity_id", unifiedHandler.GetRealtimeData)
		api.GET("/metadata/:type", unifiedHandler.GetMetadata)
	}

	return r
}
