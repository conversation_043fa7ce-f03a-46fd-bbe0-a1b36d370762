package services

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"neoapi/internal/models"
	timeparser "neoapi/internal/time"
)

// EntityQueryService 智能实体查询服务
type EntityQueryService struct {
	driver        neo4j.DriverWithContext
	timeParser    *timeparser.CSTELParser
	filterBuilder *FilterBuilder
	queryExecutor *QueryExecutor
}

// NewEntityQueryService 创建新的智能实体查询服务
func NewEntityQueryService(driver neo4j.DriverWithContext) *EntityQueryService {
	return &EntityQueryService{
		driver:        driver,
		timeParser:    timeparser.NewCSTELParser(),
		filterBuilder: NewFilterBuilder(),
		queryExecutor: NewQueryExecutor(driver),
	}
}

// QueryEntities 执行智能实体查询
func (s *EntityQueryService) QueryEntities(ctx context.Context, req *models.EntityQueryRequest) (*models.EntityQueryResponse, error) {
	startTime := time.Now()

	// 参数验证
	if err := s.validateRequest(req); err != nil {
		return nil, fmt.Errorf("invalid request: %w", err)
	}

	// 解析时间表达式
	timeQuery, err := s.timeParser.Parse(req.TimeExpression)
	if err != nil {
		return nil, fmt.Errorf("failed to parse time expression: %w", err)
	}

	if len(timeQuery.MonthNodes) == 0 {
		return nil, fmt.Errorf("no valid time periods found in expression: %s", req.TimeExpression)
	}

	// 使用第一个时间节点（当前只支持单月查询）
	period := timeQuery.MonthNodes[0]

	// 设置默认limit
	limit := req.Limit
	if limit <= 0 {
		limit = 20
	} else if limit > 100 {
		limit = 100
	}

	// 根据实体类型执行查询
	var results []models.EntityResult
	var totalFound int

	switch req.EntityType {
	case models.EntityTypeShip:
		results, totalFound, err = s.queryShips(ctx, req.Filters, period, limit)
	case models.EntityTypeShippingRoute:
		results, totalFound, err = s.queryRoutes(ctx, req.Filters, period, limit)
	case models.EntityTypePort:
		results, totalFound, err = s.queryPorts(ctx, req.Filters, period, limit)
	default:
		return nil, fmt.Errorf("unsupported entity type: %s", req.EntityType)
	}

	if err != nil {
		return nil, fmt.Errorf("query execution failed: %w", err)
	}

	executionTime := time.Since(startTime).Milliseconds()

	return &models.EntityQueryResponse{
		EntityType:      req.EntityType,
		TimePeriod:      period,
		Results:         results,
		TotalFound:      totalFound,
		Returned:        len(results),
		ExecutionTimeMs: executionTime,
	}, nil
}

// validateRequest 验证请求参数
func (s *EntityQueryService) validateRequest(req *models.EntityQueryRequest) error {
	if req.EntityType == "" {
		return fmt.Errorf("entity_type is required")
	}

	if req.TimeExpression == "" {
		return fmt.Errorf("time_expression is required")
	}

	// 验证数值范围
	filters := req.Filters
	if filters.DwtMin != nil && filters.DwtMax != nil && *filters.DwtMin > *filters.DwtMax {
		return fmt.Errorf("dwt_min cannot be greater than dwt_max")
	}

	if filters.DistanceMin != nil && filters.DistanceMax != nil && *filters.DistanceMin > *filters.DistanceMax {
		return fmt.Errorf("distance_min cannot be greater than distance_max")
	}

	if filters.BuildYearMin != nil && filters.BuildYearMax != nil && *filters.BuildYearMin > *filters.BuildYearMax {
		return fmt.Errorf("build_year_min cannot be greater than build_year_max")
	}

	if filters.MinVoyages != nil && filters.MaxVoyages != nil && *filters.MinVoyages > *filters.MaxVoyages {
		return fmt.Errorf("min_voyages cannot be greater than max_voyages")
	}

	if filters.MinCargoVolume != nil && filters.MaxCargoVolume != nil && *filters.MinCargoVolume > *filters.MaxCargoVolume {
		return fmt.Errorf("min_cargo_volume cannot be greater than max_cargo_volume")
	}

	if filters.MinLoadRatio != nil && filters.MaxLoadRatio != nil && *filters.MinLoadRatio > *filters.MaxLoadRatio {
		return fmt.Errorf("min_load_ratio cannot be greater than max_load_ratio")
	}

	if filters.MinShips != nil && filters.MaxShips != nil && *filters.MinShips > *filters.MaxShips {
		return fmt.Errorf("min_ships cannot be greater than max_ships")
	}

	return nil
}

// queryShips 查询船舶
func (s *EntityQueryService) queryShips(ctx context.Context, filters models.EntityFilters, period string, limit int) ([]models.EntityResult, int, error) {
	// 构建WHERE子句和参数
	whereClause, params := s.filterBuilder.BuildShipWhereClause(filters, period)

	// 添加基础参数
	params["period"] = period
	params["limit"] = limit

	// 构建完整查询
	query := fmt.Sprintf(`
		MATCH (s:Ship)<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
		%s
		RETURN s.name as name, s.mmsi as mmsi, s.dwt as dwt, s.regPortProvince as province,
		       sms.cargo_ton as cargo_volume, sms.voyages as voyage_count, sms.loadRatio as load_ratio
		ORDER BY sms.cargo_ton DESC, sms.voyages DESC, sms.loadRatio DESC
		LIMIT $limit
	`, whereClause)

	log.Printf("[queryShips] Executing query:\n%s\nParams: %+v", query, params)

	return s.queryExecutor.ExecuteShipQuery(ctx, query, params)
}

// queryRoutes 查询航线
func (s *EntityQueryService) queryRoutes(ctx context.Context, filters models.EntityFilters, period string, limit int) ([]models.EntityResult, int, error) {
	// 构建WHERE子句和参数
	whereClause, params := s.filterBuilder.BuildRouteWhereClause(filters, period)

	// 添加基础参数
	params["period"] = period
	params["limit"] = limit

	// 构建完整查询
	query := fmt.Sprintf(`
		MATCH (sr:ShippingRoute)<-[:ROUTE_STAT_FOR_ROUTE]-(rms:RouteMonthStat)-[:ROUTE_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
		OPTIONAL MATCH (sr)-[:ROUTE_ORIGIN]->(pO:Port)
		OPTIONAL MATCH (sr)-[:ROUTE_DESTINATION]->(pD:Port)
		%s
		RETURN sr.routeName as name, sr.routeId as route_id, sr.distance_km as distance,
		       pO.name as origin_port, pD.name as destination_port, sr.routeType as route_type,
		       rms.totalCargo_ton as total_cargo, rms.totalShipCount as ship_count, rms.totalVoyageCount as voyage_count
		ORDER BY rms.totalCargo_ton DESC, rms.totalShipCount DESC, rms.totalVoyageCount DESC
		LIMIT $limit
	`, whereClause)

	log.Printf("[queryRoutes] Executing query:\n%s\nParams: %+v", query, params)

	return s.queryExecutor.ExecuteRouteQuery(ctx, query, params)
}

// queryPorts 查询港口
func (s *EntityQueryService) queryPorts(ctx context.Context, filters models.EntityFilters, period string, limit int) ([]models.EntityResult, int, error) {
	// 构建WHERE子句和参数
	whereClause, params := s.filterBuilder.BuildPortWhereClause(filters, period)

	// 添加基础参数
	params["period"] = period
	params["limit"] = limit

	// 构建完整查询
	query := fmt.Sprintf(`
		MATCH (p:Port)<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
		%s
		RETURN p.name as name, p.prov as province,
		       (pms.inCargo_ton + pms.outCargo_ton) as total_cargo,
		       (pms.inShipCount + pms.outShipCount) as ship_count,
		       pms.inCargo_ton as in_cargo
		ORDER BY total_cargo DESC, ship_count DESC, pms.inCargo_ton DESC
		LIMIT $limit
	`, whereClause)

	log.Printf("[queryPorts] Executing query:\n%s\nParams: %+v", query, params)

	return s.queryExecutor.ExecutePortQuery(ctx, query, params)
}
