package services

import (
	"context"
	"fmt"
	"log"
	"strconv"
	"strings"

	"neoapi/internal/models"
	"neoapi/internal/utils"

	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
)

// UnifiedFallback 统一降级处理 - 简单版本
func UnifiedFallback(ctx context.Context, driver neo4j.DriverWithContext, entityType models.EntityType, entityName string, metric string, timeExpression string) *models.FallbackResult {
	log.Printf("[UnifiedFallback] Processing fallback for entity: %s, metric: %s", entityName, metric)

	// 步骤1: 尝试时间降级（去年同期）
	if fallbackData := tryTimeFallback(ctx, driver, entityType, entityName, metric, timeExpression); fallbackData != nil {
		return fallbackData
	}

	// 步骤2: 检查实体是否存在并返回基本信息
	return tryEntityExistsFallback(ctx, driver, entityType, entityName, metric)
}

// tryTimeFallback 尝试时间降级（去年同期）
func tryTimeFallback(ctx context.Context, driver neo4j.DriverWithContext, entityType models.EntityType, entityName string, metric string, timeExpression string) *models.FallbackResult {
	lastYearExpr := convertToLastYear(timeExpression)
	if lastYearExpr == "" {
		return nil // 无法转换时间，跳过时间降级
	}

	// 这里需要调用getMetricValue，但为了避免循环依赖，暂时返回nil
	// 实际实现中可以通过参数传递或其他方式解决
	log.Printf("[tryTimeFallback] Time fallback attempted for %s -> %s", timeExpression, lastYearExpr)
	
	return &models.FallbackResult{
		Value:        nil,
		Status:       "time_fallback_attempted",
		Message:      "时间降级尝试，但需要进一步实现",
		FallbackType: "time_fallback",
		AlternativeInfo: map[string]interface{}{
			"original_time": timeExpression,
			"fallback_time": lastYearExpr,
		},
	}
}

// tryEntityExistsFallback 检查实体存在性并返回基本信息
func tryEntityExistsFallback(ctx context.Context, driver neo4j.DriverWithContext, entityType models.EntityType, entityName string, metric string) *models.FallbackResult {
	exists, basicInfo := checkEntityExistsWithBasicInfo(ctx, driver, entityType, entityName)

	if exists {
		return &models.FallbackResult{
			Value:           nil,
			Status:          "metric_unavailable_entity_exists",
			Message:         fmt.Sprintf("指标'%s'不可用，但实体存在", metric),
			FallbackType:    "entity_exists",
			AlternativeInfo: basicInfo,
		}
	}

	return &models.FallbackResult{
		Value:        nil,
		Status:       "entity_not_found",
		Message:      fmt.Sprintf("实体'%s'不存在", entityName),
		FallbackType: "not_found",
	}
}

// convertToLastYear 简单的去年时间转换
func convertToLastYear(timeExpression string) string {
	// R6M 等相对时间保持不变（系统会自动计算去年同期）
	if strings.HasPrefix(timeExpression, "R") {
		return timeExpression
	}

	// M202508 -> M202408 (月份格式转换)
	if strings.HasPrefix(timeExpression, "M") && len(timeExpression) == 7 {
		year := timeExpression[1:5]
		month := timeExpression[5:7]
		if yearInt, err := strconv.Atoi(year); err == nil {
			return fmt.Sprintf("M%04d%s", yearInt-1, month)
		}
	}

	return "" // 无法转换的格式
}

// checkEntityExistsWithBasicInfo 检查实体存在性并获取基本信息
func checkEntityExistsWithBasicInfo(ctx context.Context, driver neo4j.DriverWithContext, entityType models.EntityType, entityName string) (bool, map[string]interface{}) {
	session := driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	var query string
	switch entityType {
	case models.EntityTypeShip:
		query = `
			MATCH (s:Ship {name: $entityName})
			RETURN s.name as name, s.mmsi as mmsi, s.owner as owner
		`
	case models.EntityTypePort:
		query = `
			MATCH (p:Port {name: $entityName})
			OPTIONAL MATCH (p)-[:BELONGS_TO_PROVINCE]->(prov:Province)
			RETURN p.name as name, p.code as code, prov.name as province
		`
	default:
		return false, nil
	}

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		params := map[string]interface{}{"entityName": entityName}
		log.Printf("[checkEntityExistsWithBasicInfo] Executing Cypher query:\n%s\nParams: %+v", query, params)

		records, err := tx.Run(ctx, query, params)
		if err != nil {
			return nil, err
		}

		if !records.Next(ctx) {
			return nil, nil // 实体不存在
		}

		record := records.Record()
		basicInfo := map[string]interface{}{
			"entity_type": string(entityType),
		}

		// 添加查询到的基本信息
		for i, key := range record.Keys {
			if record.Values[i] != nil {
				basicInfo[key] = utils.GetStringValue(record.Values[i])
			}
		}

		return basicInfo, nil
	})

	if err != nil || result == nil {
		return false, nil
	}

	return true, result.(map[string]interface{})
}
