package services

import (
	"fmt"
	"strings"

	"neoapi/internal/models"
)

// FilterBuilder 过滤条件构建器
type FilterBuilder struct{}

// NewFilterBuilder 创建新的过滤条件构建器
func NewFilterBuilder() *FilterBuilder {
	return &FilterBuilder{}
}

// BuildShipWhereClause 构建船舶查询的WHERE子句
func (fb *FilterBuilder) BuildShipWhereClause(filters models.EntityFilters, period string) (string, map[string]interface{}) {
	var conditions []string
	params := make(map[string]interface{})

	// 基础条件：确保有统计数据
	// conditions = append(conditions, "sms.cargo_ton IS NOT NULL")

	// 文本搜索
	if filters.Name != "" {
		conditions = append(conditions, "(toLower(s.name) CONTAINS toLower($name) OR s.mmsi CONTAINS $name)")
		params["name"] = filters.Name
	}

	// MMSI精确匹配
	if filters.MMSI != "" {
		conditions = append(conditions, "s.mmsi = $mmsi")
		params["mmsi"] = filters.MMSI
	}

	// 载重吨范围
	if filters.DwtMin != nil {
		conditions = append(conditions, "s.dwt >= $dwt_min")
		params["dwt_min"] = *filters.DwtMin
	}
	if filters.DwtMax != nil {
		conditions = append(conditions, "s.dwt <= $dwt_max")
		params["dwt_max"] = *filters.DwtMax
	}

	// 建造年份范围
	if filters.BuildYearMin != nil {
		conditions = append(conditions, "date(s.builtDate).year >= $build_year_min")
		params["build_year_min"] = *filters.BuildYearMin
	}
	if filters.BuildYearMax != nil {
		conditions = append(conditions, "date(s.builtDate).year <= $build_year_max")
		params["build_year_max"] = *filters.BuildYearMax
	}

	// 省份过滤
	if len(filters.Provinces) > 0 {
		conditions = append(conditions, "s.regPortProvince IN $provinces")
		params["provinces"] = filters.Provinces
	}

	// 船舶类型过滤
	if len(filters.ShipTypes) > 0 {
		conditions = append(conditions, `EXISTS {
			MATCH (s)-[:IS_TYPE]->(st:ShipType)
			WHERE st.subName IN $ship_types
		}`)
		params["ship_types"] = filters.ShipTypes
	}

	// 活跃航线过滤
	if len(filters.ActiveRoutes) > 0 {
		subConditions := []string{"sr.routeName IN $active_routes"}
		if filters.MinVoyages != nil {
			subConditions = append(subConditions, "smls.voyageCount >= $min_voyages_route")
			params["min_voyages_route"] = *filters.MinVoyages
		}

		conditions = append(conditions, fmt.Sprintf(`EXISTS {
			MATCH (s)<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)
			-[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
			-[:LINE_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
			WHERE %s
		}`, strings.Join(subConditions, " AND ")))
		params["active_routes"] = filters.ActiveRoutes
	}

	// 货物类型过滤
	if len(filters.CargoTypes) > 0 {
		conditions = append(conditions, `EXISTS {
			MATCH (s)<-[:CARGO_STAT_FOR_SHIP]-(smcs:ShipMonthCargoStat)
			-[:CARGO_STAT_FOR_TYPE]->(ct:CargoType)
			-[:CARGO_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
			WHERE ct.subName IN $cargo_types
		}`)
		params["cargo_types"] = filters.CargoTypes
	}

	// 性能阈值过滤
	if filters.MinVoyages != nil && len(filters.ActiveRoutes) == 0 {
		conditions = append(conditions, "sms.voyages >= $min_voyages")
		params["min_voyages"] = *filters.MinVoyages
	}
	if filters.MaxVoyages != nil {
		conditions = append(conditions, "sms.voyages <= $max_voyages")
		params["max_voyages"] = *filters.MaxVoyages
	}
	if filters.MinCargoVolume != nil {
		conditions = append(conditions, "sms.cargo_ton >= $min_cargo_volume")
		params["min_cargo_volume"] = *filters.MinCargoVolume
	}
	if filters.MaxCargoVolume != nil {
		conditions = append(conditions, "sms.cargo_ton <= $max_cargo_volume")
		params["max_cargo_volume"] = *filters.MaxCargoVolume
	}
	if filters.MinLoadRatio != nil {
		conditions = append(conditions, "sms.loadRatio >= $min_load_ratio")
		params["min_load_ratio"] = *filters.MinLoadRatio
	}
	if filters.MaxLoadRatio != nil {
		conditions = append(conditions, "sms.loadRatio <= $max_load_ratio")
		params["max_load_ratio"] = *filters.MaxLoadRatio
	}

	// 排除条件
	if len(filters.ExcludeNames) > 0 {
		conditions = append(conditions, "NOT s.name IN $exclude_names")
		params["exclude_names"] = filters.ExcludeNames
	}

	// 构建WHERE子句
	if len(conditions) > 0 {
		return "WHERE " + strings.Join(conditions, " AND "), params
	}
	return "", params
}

// BuildRouteWhereClause 构建航线查询的WHERE子句
func (fb *FilterBuilder) BuildRouteWhereClause(filters models.EntityFilters, period string) (string, map[string]interface{}) {
	var conditions []string
	params := make(map[string]interface{})

	// 基础条件：确保有统计数据
	conditions = append(conditions, "rms.totalCargo_ton IS NOT NULL")

	// 文本搜索
	if filters.Name != "" {
		conditions = append(conditions, "(toLower(sr.routeName) CONTAINS toLower($name) OR sr.routeId CONTAINS $name)")
		params["name"] = filters.Name
	}

	// 航线类型过滤
	if len(filters.RouteTypes) > 0 {
		conditions = append(conditions, "sr.routeType IN $route_types")
		params["route_types"] = filters.RouteTypes
	}

	// 距离范围过滤
	if filters.DistanceMin != nil {
		conditions = append(conditions, "sr.distance_km >= $distance_min")
		params["distance_min"] = *filters.DistanceMin
	}
	if filters.DistanceMax != nil {
		conditions = append(conditions, "sr.distance_km <= $distance_max")
		params["distance_max"] = *filters.DistanceMax
	}

	// 起点港口过滤
	if len(filters.OriginPorts) > 0 {
		conditions = append(conditions, "pO.name IN $origin_ports")
		params["origin_ports"] = filters.OriginPorts
	}

	// 终点港口过滤
	if len(filters.DestinationPorts) > 0 {
		conditions = append(conditions, "pD.name IN $destination_ports")
		params["destination_ports"] = filters.DestinationPorts
	}

	// 性能阈值过滤
	if filters.MinShips != nil {
		conditions = append(conditions, "rms.totalShipCount >= $min_ships")
		params["min_ships"] = *filters.MinShips
	}
	if filters.MaxShips != nil {
		conditions = append(conditions, "rms.totalShipCount <= $max_ships")
		params["max_ships"] = *filters.MaxShips
	}
	if filters.MinCargoVolume != nil {
		conditions = append(conditions, "rms.totalCargo_ton >= $min_cargo_volume")
		params["min_cargo_volume"] = *filters.MinCargoVolume
	}
	if filters.MaxCargoVolume != nil {
		conditions = append(conditions, "rms.totalCargo_ton <= $max_cargo_volume")
		params["max_cargo_volume"] = *filters.MaxCargoVolume
	}
	if filters.MinVoyages != nil {
		conditions = append(conditions, "rms.totalVoyageCount >= $min_voyages")
		params["min_voyages"] = *filters.MinVoyages
	}
	if filters.MaxVoyages != nil {
		conditions = append(conditions, "rms.totalVoyageCount <= $max_voyages")
		params["max_voyages"] = *filters.MaxVoyages
	}

	// 排除条件
	if len(filters.ExcludeNames) > 0 {
		conditions = append(conditions, "NOT sr.routeName IN $exclude_names")
		params["exclude_names"] = filters.ExcludeNames
	}
	if len(filters.ExcludeRoutes) > 0 {
		conditions = append(conditions, "NOT sr.routeName IN $exclude_routes")
		params["exclude_routes"] = filters.ExcludeRoutes
	}

	// 构建WHERE子句
	if len(conditions) > 0 {
		return "WHERE " + strings.Join(conditions, " AND "), params
	}
	return "", params
}

// BuildPortWhereClause 构建港口查询的WHERE子句
func (fb *FilterBuilder) BuildPortWhereClause(filters models.EntityFilters, period string) (string, map[string]interface{}) {
	var conditions []string
	params := make(map[string]interface{})

	// 基础条件：确保有统计数据
	conditions = append(conditions, "(pms.inCargo_ton IS NOT NULL OR pms.outCargo_ton IS NOT NULL)")

	// 文本搜索
	if filters.Name != "" {
		conditions = append(conditions, "toLower(p.name) CONTAINS toLower($name)")
		params["name"] = filters.Name
	}

	// 省份过滤
	if len(filters.Provinces) > 0 {
		conditions = append(conditions, "p.prov IN $provinces")
		params["provinces"] = filters.Provinces
	}

	// 性能阈值过滤
	if filters.MinCargoVolume != nil {
		conditions = append(conditions, "(pms.inCargo_ton + pms.outCargo_ton) >= $min_cargo_volume")
		params["min_cargo_volume"] = *filters.MinCargoVolume
	}
	if filters.MaxCargoVolume != nil {
		conditions = append(conditions, "(pms.inCargo_ton + pms.outCargo_ton) <= $max_cargo_volume")
		params["max_cargo_volume"] = *filters.MaxCargoVolume
	}
	if filters.MinShips != nil {
		conditions = append(conditions, "(pms.inShipCount + pms.outShipCount) >= $min_ships")
		params["min_ships"] = *filters.MinShips
	}
	if filters.MaxShips != nil {
		conditions = append(conditions, "(pms.inShipCount + pms.outShipCount) <= $max_ships")
		params["max_ships"] = *filters.MaxShips
	}

	// 排除条件
	if len(filters.ExcludeNames) > 0 {
		conditions = append(conditions, "NOT p.name IN $exclude_names")
		params["exclude_names"] = filters.ExcludeNames
	}
	if len(filters.ExcludePorts) > 0 {
		conditions = append(conditions, "NOT p.name IN $exclude_ports")
		params["exclude_ports"] = filters.ExcludePorts
	}

	// 构建WHERE子句
	if len(conditions) > 0 {
		return "WHERE " + strings.Join(conditions, " AND "), params
	}
	return "", params
}
