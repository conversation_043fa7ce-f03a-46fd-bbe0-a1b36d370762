package services

import (
	"fmt"
	"strings"

	"neoapi/internal/models"
)

// MetricConfig 指标配置
type MetricConfig struct {
	ChineseName string              `json:"chinese_name"`         // 中文显示名称
	EnglishName string              `json:"english_name"`         // 英文名称
	DBField     string              `json:"db_field"`             // 数据库字段名（单一指标）
	DBFields    []string            `json:"db_fields"`            // 数据库字段名列表（复合指标）
	Unit        string              `json:"unit"`                 // 单位
	EntityTypes []models.EntityType `json:"entity_types"`         // 适用的实体类型
	Description string              `json:"description"`          // 指标描述
	Category    string              `json:"category"`             // 指标分类
	Aliases     []string            `json:"aliases"`              // 别名
	IsComposite bool                `json:"is_composite"`         // 是否为复合指标
	Components  []string            `json:"components,omitempty"` // 复合指标的组件列表（指标名称）
}

// MetricRegistry 指标注册表
type MetricRegistry struct {
	metrics      map[string]*MetricConfig              // 按中文名称索引
	byDBField    map[string]*MetricConfig              // 按数据库字段索引
	byCategory   map[string][]*MetricConfig            // 按分类索引
	byEntityType map[models.EntityType][]*MetricConfig // 按实体类型索引
	aliases      map[string]string                     // 别名映射到标准名称
}

// NewMetricRegistry 创建新的指标注册表
func NewMetricRegistry() *MetricRegistry {
	registry := &MetricRegistry{
		metrics:      make(map[string]*MetricConfig),
		byDBField:    make(map[string]*MetricConfig),
		byCategory:   make(map[string][]*MetricConfig),
		byEntityType: make(map[models.EntityType][]*MetricConfig),
		aliases:      make(map[string]string),
	}

	// 加载默认指标配置
	registry.loadDefaultMetrics()

	return registry
}

// loadDefaultMetrics 加载默认指标配置（基于数据库设计文档）
func (mr *MetricRegistry) loadDefaultMetrics() {
	// 船舶指标（基于ShipMonthStat节点属性）
	shipMetrics := []*MetricConfig{
		{
			ChineseName: "有效营运率",
			EnglishName: "Operational Ratio",
			DBField:     "opRatio",
			Unit:        "比率",
			EntityTypes: []models.EntityType{models.EntityTypeShip},
			Description: "船舶有效营运时间占总时间的比例",
			Category:    "运营效率",
			Aliases:     []string{"营运率", "运营率"},
		},
		{
			ChineseName: "航次数",
			EnglishName: "Voyage Count",
			DBField:     "voyages",
			Unit:        "次",
			EntityTypes: []models.EntityType{models.EntityTypeShip},
			Description: "月度航行次数",
			Category:    "运营数据",
			Aliases:     []string{"航行次数", "航次"},
		},
		{
			ChineseName: "载重率",
			EnglishName: "Load Ratio",
			DBField:     "loadRatio",
			Unit:        "比率",
			EntityTypes: []models.EntityType{models.EntityTypeShip},
			Description: "实际载重与额定载重的比例",
			Category:    "运营效率",
			Aliases:     []string{"负载率", "装载率"},
		},
		{
			ChineseName: "载货量",
			EnglishName: "Cargo Capacity",
			DBField:     "capacity_ton",
			Unit:        "吨",
			EntityTypes: []models.EntityType{models.EntityTypeShip},
			Description: "月度总载货量",
			Category:    "运营数据",
			Aliases:     []string{"货运量", "运量"},
		},
		{
			ChineseName: "周转量",
			EnglishName: "Turnover",
			DBField:     "turnover_tonkm",
			Unit:        "吨公里",
			EntityTypes: []models.EntityType{models.EntityTypeShip},
			Description: "货物周转量",
			Category:    "运营数据",
			Aliases:     []string{"货物周转量"},
		},
		{
			ChineseName: "在港时间",
			EnglishName: "Anchor Time",
			DBField:     "anchorTime_day",
			Unit:        "天",
			EntityTypes: []models.EntityType{models.EntityTypeShip},
			Description: "船舶在港停泊时间",
			Category:    "运营数据",
			Aliases:     []string{"停泊时间", "锚泊时间"},
		},
		{
			ChineseName: "航行时间",
			EnglishName: "Sail Time",
			DBField:     "sailTime_day",
			Unit:        "天",
			EntityTypes: []models.EntityType{models.EntityTypeShip},
			Description: "船舶航行时间",
			Category:    "运营数据",
			Aliases:     []string{"航行天数"},
		},
		{
			ChineseName: "航行率",
			EnglishName: "Sail Ratio",
			DBField:     "sailRatio",
			Unit:        "比率",
			EntityTypes: []models.EntityType{models.EntityTypeShip},
			Description: "航行时间占总时间的比例",
			Category:    "运营效率",
			Aliases:     []string{"航行比率"},
		},
		{
			ChineseName: "负载航次数",
			EnglishName: "Load Voyage Count",
			DBField:     "loadVoyages",
			Unit:        "次",
			EntityTypes: []models.EntityType{models.EntityTypeShip},
			Description: "有载货的航次数量",
			Category:    "运营数据",
			Aliases:     []string{"载货航次数"},
		},
		{
			ChineseName: "运距",
			EnglishName: "Mileage",
			DBField:     "mileage_km",
			Unit:        "公里",
			EntityTypes: []models.EntityType{models.EntityTypeShip},
			Description: "总运输距离",
			Category:    "运营数据",
			Aliases:     []string{"航行距离", "里程"},
		},
	}

	// 港口指标（基于PortMonthStat节点属性）
	portMetrics := []*MetricConfig{
		{
			ChineseName: "进港艘次",
			EnglishName: "Inbound Ship Count",
			DBField:     "inShipCount",
			Unit:        "艘次",
			EntityTypes: []models.EntityType{models.EntityTypePort},
			Description: "月度进港船舶数量",
			Category:    "港口运营",
			Aliases:     []string{"进港船舶数", "入港艘次"},
		},
		{
			ChineseName: "出港艘次",
			EnglishName: "Outbound Ship Count",
			DBField:     "outShipCount",
			Unit:        "艘次",
			EntityTypes: []models.EntityType{models.EntityTypePort},
			Description: "月度出港船舶数量",
			Category:    "港口运营",
			Aliases:     []string{"出港船舶数"},
		},
		{
			ChineseName: "进港货量",
			EnglishName: "Inbound Cargo",
			DBField:     "inCargo_ton",
			Unit:        "吨",
			EntityTypes: []models.EntityType{models.EntityTypePort},
			Description: "月度进港货物总量",
			Category:    "港口运营",
			Aliases:     []string{"进港运量", "入港货量"},
		},
		{
			ChineseName: "出港货量",
			EnglishName: "Outbound Cargo",
			DBField:     "outCargo_ton",
			Unit:        "吨",
			EntityTypes: []models.EntityType{models.EntityTypePort},
			Description: "月度出港货物总量",
			Category:    "港口运营",
			Aliases:     []string{"出港运量"},
		},
		{
			ChineseName: "总吞吐量",
			EnglishName: "Total Throughput",
			DBField:     "totalThroughput_ton",
			Unit:        "吨",
			EntityTypes: []models.EntityType{models.EntityTypePort},
			Description: "进出港货量总和",
			Category:    "港口运营",
			Aliases:     []string{"吞吐量", "总运量"},
		},
		{
			ChineseName: "进港装载率",
			EnglishName: "Inbound Load Ratio",
			DBField:     "inLoadRatio",
			Unit:        "比率",
			EntityTypes: []models.EntityType{models.EntityTypePort},
			Description: "进港船舶平均装载率",
			Category:    "港口效率",
			Aliases:     []string{"进港负载率"},
		},
		{
			ChineseName: "出港装载率",
			EnglishName: "Outbound Load Ratio",
			DBField:     "outLoadRatio",
			Unit:        "比率",
			EntityTypes: []models.EntityType{models.EntityTypePort},
			Description: "出港船舶平均装载率",
			Category:    "港口效率",
			Aliases:     []string{"出港负载率"},
		},
	}

	// 复合指标定义
	compositeMetrics := []*MetricConfig{
		{
			ChineseName: "吞吐量",
			EnglishName: "Throughput Details",
			DBField:     "", // 复合指标没有单一的数据库字段
			Unit:        "吨",
			EntityTypes: []models.EntityType{models.EntityTypePort},
			Description: "港口吞吐量的完整详情，包含进港、出港和总吞吐量",
			Category:    "港口运营",
			Aliases:     []string{"吞吐量", "港口吞吐量"},
			IsComposite: true,
			Components:  []string{"进港货量", "出港货量"},
		},
		{
			ChineseName: "装载率",
			EnglishName: "Load Ratio Details",
			DBField:     "", // 复合指标没有单一的数据库字段
			Unit:        "比率",
			EntityTypes: []models.EntityType{models.EntityTypePort},
			Description: "港口装载率的完整详情，包含进港和出港装载率",
			Category:    "港口效率",
			Aliases:     []string{"装载率", "负载率"},
			IsComposite: true,
			Components:  []string{"进港装载率", "出港装载率"},
		},
		{
			ChineseName: "船舶运营详情",
			EnglishName: "Ship Operation Details",
			DBField:     "", // 复合指标没有单一的数据库字段
			Unit:        "混合",
			EntityTypes: []models.EntityType{models.EntityTypeShip},
			Description: "船舶运营的完整详情，包含关键运营指标",
			Category:    "运营效率",
			Aliases:     []string{"运营详情", "船舶指标"},
			IsComposite: true,
			Components:  []string{"有效营运率", "载重率", "航次数"},
		},
	}

	// 航线指标（基于RouteMonthStat节点属性）
	routeMetrics := []*MetricConfig{
		{
			ChineseName: "航线货运量",
			EnglishName: "Route Cargo Volume",
			DBField:     "totalCargo_ton",
			Unit:        "吨",
			EntityTypes: []models.EntityType{models.EntityTypeShippingRoute},
			Description: "该航线总货运量",
			Category:    "航线运营",
			Aliases:     []string{"货运量", "运量"},
		},
		{
			ChineseName: "航行船舶数",
			EnglishName: "Ship Count",
			DBField:     "totalShipCount",
			Unit:        "艘",
			EntityTypes: []models.EntityType{models.EntityTypeShippingRoute},
			Description: "在该航线航行的船舶数",
			Category:    "航线运营",
			Aliases:     []string{"船舶数", "运营船舶数"},
		},
		{
			ChineseName: "航次数",
			EnglishName: "Voyage Count",
			DBField:     "totalVoyageCount",
			Unit:        "次",
			EntityTypes: []models.EntityType{models.EntityTypeShippingRoute},
			Description: "该航线总航次数",
			Category:    "航线运营",
			Aliases:     []string{"总航次数"},
		},
		{
			ChineseName: "平均载重率",
			EnglishName: "Average Load Ratio",
			DBField:     "avgLoadRatio",
			Unit:        "比率",
			EntityTypes: []models.EntityType{models.EntityTypeShippingRoute},
			Description: "该航线船舶平均载重率",
			Category:    "航线效率",
			Aliases:     []string{"载重率", "装载率"},
		},
	}

	// // 货物类型指标（基于各种CargoStat节点）
	// cargoTypeMetrics := []*MetricConfig{
	// 	{
	// 		ChineseName: "运输量",
	// 		EnglishName: "Transportation Volume",
	// 		DBField:     "cargo_ton",
	// 		Unit:        "吨",
	// 		EntityTypes: []models.EntityType{models.EntityTypeCargoType},
	// 		Description: "该货物类型的总运输量",
	// 		Category:    "货物运输",
	// 		Aliases:     []string{"货运量", "运量", "总运量"},
	// 	},
	// 	{
	// 		ChineseName: "运输船舶数",
	// 		EnglishName: "Transportation Ship Count",
	// 		DBField:     "ship_count",
	// 		Unit:        "艘",
	// 		EntityTypes: []models.EntityType{models.EntityTypeCargoType},
	// 		Description: "运输该货物类型的船舶数量",
	// 		Category:    "货物运输",
	// 		Aliases:     []string{"船舶数", "承运船舶数"},
	// 	},
	// 	{
	// 		ChineseName: "运输航次数",
	// 		EnglishName: "Transportation Voyage Count",
	// 		DBField:     "voyage_count",
	// 		Unit:        "次",
	// 		EntityTypes: []models.EntityType{models.EntityTypeCargoType},
	// 		Description: "运输该货物类型的航次数量",
	// 		Category:    "货物运输",
	// 		Aliases:     []string{"航次数", "运输次数"},
	// 	},
	// }

	// // 船舶货物相关指标（需要过滤器支持）
	// shipCargoMetrics := []*MetricConfig{
	// 	{
	// 		ChineseName: "分货类运量",
	// 		EnglishName: "Cargo Type Specific Volume",
	// 		DBField:     "cargo_ton",
	// 		Unit:        "吨",
	// 		EntityTypes: []models.EntityType{models.EntityTypeShip},
	// 		Description: "船舶运输特定货物类型的运量（需要cargo_type过滤器）",
	// 		Category:    "船舶货运",
	// 		Aliases:     []string{"货类运量", "特定货物运量"},
	// 	},
	// }

	// 注册所有指标
	allMetrics := append(shipMetrics, portMetrics...)
	allMetrics = append(allMetrics, routeMetrics...)
	allMetrics = append(allMetrics, compositeMetrics...)

	for _, metric := range allMetrics {
		mr.registerMetric(metric)
	}
}

// registerMetric 注册单个指标
func (mr *MetricRegistry) registerMetric(config *MetricConfig) {
	// 主名称索引
	mr.metrics[config.ChineseName] = config

	// 数据库字段索引
	mr.byDBField[config.DBField] = config

	// 分类索引
	mr.byCategory[config.Category] = append(mr.byCategory[config.Category], config)

	// 实体类型索引
	for _, entityType := range config.EntityTypes {
		mr.byEntityType[entityType] = append(mr.byEntityType[entityType], config)
	}

	// 别名索引
	for _, alias := range config.Aliases {
		mr.aliases[alias] = config.ChineseName
	}
}

// GetMetricConfig 根据中文名称获取指标配置
func (mr *MetricRegistry) GetMetricConfig(chineseName string) (*MetricConfig, error) {
	// 先尝试直接匹配
	if config, exists := mr.metrics[chineseName]; exists {
		return config, nil
	}

	// 尝试别名匹配
	if standardName, exists := mr.aliases[chineseName]; exists {
		if config, exists := mr.metrics[standardName]; exists {
			return config, nil
		}
	}

	// 尝试模糊匹配（去除空格、标点符号）
	normalizedInput := normalizeMetricName(chineseName)
	for name, config := range mr.metrics {
		if normalizeMetricName(name) == normalizedInput {
			return config, nil
		}
	}

	return nil, fmt.Errorf("未找到指标: %s", chineseName)
}

// GetDBField 根据中文名称获取数据库字段名
func (mr *MetricRegistry) GetDBField(chineseName string) (string, error) {
	config, err := mr.GetMetricConfig(chineseName)
	if err != nil {
		return "", err
	}
	return config.DBField, nil
}

// GetUnit 根据中文名称获取单位
func (mr *MetricRegistry) GetUnit(chineseName string) (string, error) {
	config, err := mr.GetMetricConfig(chineseName)
	if err != nil {
		return "", err
	}
	return config.Unit, nil
}

// GetAvailableMetrics 获取指定实体类型的所有可用指标
func (mr *MetricRegistry) GetAvailableMetrics(entityType models.EntityType) []*MetricConfig {
	return mr.byEntityType[entityType]
}

// ValidateMetric 验证指标是否适用于指定实体类型
func (mr *MetricRegistry) ValidateMetric(chineseName string, entityType models.EntityType) error {
	config, err := mr.GetMetricConfig(chineseName)
	if err != nil {
		return err
	}

	for _, supportedType := range config.EntityTypes {
		if supportedType == entityType {
			return nil
		}
	}

	return fmt.Errorf("指标'%s'不适用于实体类型'%s'", chineseName, entityType)
}

// GetMetricsByCategory 根据分类获取指标
func (mr *MetricRegistry) GetMetricsByCategory(category string) []*MetricConfig {
	return mr.byCategory[category]
}

// GetAllCategories 获取所有指标分类
func (mr *MetricRegistry) GetAllCategories() []string {
	categories := make([]string, 0, len(mr.byCategory))
	for category := range mr.byCategory {
		categories = append(categories, category)
	}
	return categories
}

// SearchMetrics 搜索指标（支持模糊匹配）
func (mr *MetricRegistry) SearchMetrics(keyword string) []*MetricConfig {
	var results []*MetricConfig
	normalizedKeyword := normalizeMetricName(keyword)

	for _, config := range mr.metrics {
		// 检查中文名称
		if strings.Contains(normalizeMetricName(config.ChineseName), normalizedKeyword) {
			results = append(results, config)
			continue
		}

		// 检查英文名称
		if strings.Contains(strings.ToLower(config.EnglishName), strings.ToLower(keyword)) {
			results = append(results, config)
			continue
		}

		// 检查描述
		if strings.Contains(normalizeMetricName(config.Description), normalizedKeyword) {
			results = append(results, config)
			continue
		}

		// 检查别名
		for _, alias := range config.Aliases {
			if strings.Contains(normalizeMetricName(alias), normalizedKeyword) {
				results = append(results, config)
				break
			}
		}
	}

	return results
}

// normalizeMetricName 标准化指标名称（去除空格、标点符号）
func normalizeMetricName(name string) string {
	// 去除常见的标点符号和空格
	replacer := strings.NewReplacer(
		" ", "",
		"　", "", // 全角空格
		"(", "",
		")", "",
		"（", "",
		"）", "",
		"-", "",
		"_", "",
		".", "",
		"。", "",
		",", "",
		"，", "",
	)
	return replacer.Replace(name)
}

// GetMetricConfigByDBField 根据数据库字段获取指标配置
func (mr *MetricRegistry) GetMetricConfigByDBField(dbField string) (*MetricConfig, error) {
	if config, exists := mr.byDBField[dbField]; exists {
		return config, nil
	}
	return nil, fmt.Errorf("未找到数据库字段对应的指标: %s", dbField)
}
