package services

import (
	"fmt"
	"log"

	"neoapi/internal/models"
)

// MetricService 指标服务
type MetricService struct {
	registry *MetricRegistry
}

// NewMetricService 创建新的指标服务
func NewMetricService() *MetricService {
	return &MetricService{
		registry: NewMetricRegistry(),
	}
}

// GetMetricConfig 获取指标配置
func (ms *MetricService) GetMetricConfig(chineseName string) (*MetricConfig, error) {
	return ms.registry.GetMetricConfig(chineseName)
}

// GetDBField 获取数据库字段名
func (ms *MetricService) GetDBField(chineseName string) (string, error) {
	return ms.registry.GetDBField(chineseName)
}

// GetUnit 获取指标单位
func (ms *MetricService) GetUnit(chineseName string) (string, error) {
	return ms.registry.GetUnit(chineseName)
}

// ValidateMetric 验证指标是否适用于指定实体类型
func (ms *MetricService) ValidateMetric(chineseName string, entityType models.EntityType) error {
	return ms.registry.ValidateMetric(chineseName, entityType)
}

// GetAvailableMetrics 获取指定实体类型的所有可用指标
func (ms *MetricService) GetAvailableMetrics(entityType models.EntityType) []*MetricConfig {
	return ms.registry.GetAvailableMetrics(entityType)
}

// SearchMetrics 搜索指标
func (ms *MetricService) SearchMetrics(keyword string) []*MetricConfig {
	return ms.registry.SearchMetrics(keyword)
}

// GetMetricsByCategory 根据分类获取指标
func (ms *MetricService) GetMetricsByCategory(category string) []*MetricConfig {
	return ms.registry.GetMetricsByCategory(category)
}

// GetAllCategories 获取所有指标分类
func (ms *MetricService) GetAllCategories() []string {
	return ms.registry.GetAllCategories()
}

// ResolveMetricName 解析指标名称（支持别名和模糊匹配）
func (ms *MetricService) ResolveMetricName(input string, entityType models.EntityType) (string, error) {
	// 先验证指标是否适用于实体类型
	if err := ms.ValidateMetric(input, entityType); err != nil {
		return "", err
	}

	// 获取标准化的指标配置
	config, err := ms.GetMetricConfig(input)
	if err != nil {
		return "", err
	}

	return config.ChineseName, nil
}

// GetMetricInfo 获取指标的完整信息
func (ms *MetricService) GetMetricInfo(chineseName string) (*MetricInfo, error) {
	config, err := ms.GetMetricConfig(chineseName)
	if err != nil {
		return nil, err
	}

	return &MetricInfo{
		ChineseName: config.ChineseName,
		EnglishName: config.EnglishName,
		DBField:     config.DBField,
		Unit:        config.Unit,
		Description: config.Description,
		Category:    config.Category,
		EntityTypes: config.EntityTypes,
		Aliases:     config.Aliases,
	}, nil
}

// MetricInfo 指标信息（用于API返回）
type MetricInfo struct {
	ChineseName string              `json:"chinese_name"`
	EnglishName string              `json:"english_name"`
	DBField     string              `json:"db_field"`
	Unit        string              `json:"unit"`
	Description string              `json:"description"`
	Category    string              `json:"category"`
	EntityTypes []models.EntityType `json:"entity_types"`
	Aliases     []string            `json:"aliases"`
}

// GetEntityMetricsInfo 获取实体类型的所有指标信息
func (ms *MetricService) GetEntityMetricsInfo(entityType models.EntityType) ([]*MetricInfo, error) {
	configs := ms.GetAvailableMetrics(entityType)
	if len(configs) == 0 {
		return nil, fmt.Errorf("实体类型'%s'没有可用的指标", entityType)
	}

	var infos []*MetricInfo
	for _, config := range configs {
		info := &MetricInfo{
			ChineseName: config.ChineseName,
			EnglishName: config.EnglishName,
			DBField:     config.DBField,
			Unit:        config.Unit,
			Description: config.Description,
			Category:    config.Category,
			EntityTypes: config.EntityTypes,
			Aliases:     config.Aliases,
		}
		infos = append(infos, info)
	}

	return infos, nil
}

// ValidateAndNormalizeMetric 验证并标准化指标名称
func (ms *MetricService) ValidateAndNormalizeMetric(input string, entityType models.EntityType) (string, string, string, error) {
	// 解析指标名称
	standardName, err := ms.ResolveMetricName(input, entityType)
	if err != nil {
		return "", "", "", err
	}

	// 获取数据库字段和单位
	dbField, err := ms.GetDBField(standardName)
	if err != nil {
		return "", "", "", err
	}

	unit, err := ms.GetUnit(standardName)
	if err != nil {
		return "", "", "", err
	}

	log.Printf("[MetricService] Normalized metric: %s -> %s (DB: %s, Unit: %s)", input, standardName, dbField, unit)

	return standardName, dbField, unit, nil
}

// GetMetricSuggestions 获取指标建议（用于自动补全）
func (ms *MetricService) GetMetricSuggestions(input string, entityType models.EntityType, limit int) []*MetricInfo {
	// 获取该实体类型的所有指标
	configs := ms.GetAvailableMetrics(entityType)

	var suggestions []*MetricInfo
	normalizedInput := normalizeMetricName(input)

	// 优先级匹配
	for _, config := range configs {
		// 1. 精确匹配
		if config.ChineseName == input {
			suggestions = append([]*MetricInfo{{
				ChineseName: config.ChineseName,
				EnglishName: config.EnglishName,
				Unit:        config.Unit,
				Description: config.Description,
			}}, suggestions...)
			continue
		}

		// 2. 前缀匹配
		if len(normalizedInput) > 0 &&
			len(normalizeMetricName(config.ChineseName)) >= len(normalizedInput) &&
			normalizeMetricName(config.ChineseName)[:len(normalizedInput)] == normalizedInput {
			suggestions = append(suggestions, &MetricInfo{
				ChineseName: config.ChineseName,
				EnglishName: config.EnglishName,
				Unit:        config.Unit,
				Description: config.Description,
			})
			continue
		}

		// 3. 包含匹配
		if len(normalizedInput) > 0 &&
			len(normalizedInput) >= 2 && // 至少2个字符才进行包含匹配
			contains(normalizeMetricName(config.ChineseName), normalizedInput) {
			suggestions = append(suggestions, &MetricInfo{
				ChineseName: config.ChineseName,
				EnglishName: config.EnglishName,
				Unit:        config.Unit,
				Description: config.Description,
			})
		}

		// 限制返回数量
		if len(suggestions) >= limit {
			break
		}
	}

	return suggestions
}

// contains 检查字符串是否包含子串（忽略大小写）
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		len(substr) > 0 &&
		findSubstring(s, substr)
}

// findSubstring 查找子串
func findSubstring(s, substr string) bool {
	if len(substr) > len(s) {
		return false
	}

	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// RefreshRegistry 刷新指标注册表（用于热重载）
func (ms *MetricService) RefreshRegistry() {
	log.Println("[MetricService] Refreshing metric registry...")
	ms.registry = NewMetricRegistry()
	log.Println("[MetricService] Metric registry refreshed successfully")
}

// IsCompositeMetric 检查指标是否为复合指标
func (ms *MetricService) IsCompositeMetric(chineseName string) bool {
	config, err := ms.GetMetricConfig(chineseName)
	if err != nil {
		return false
	}
	return config.IsComposite
}

// GetCompositeComponents 获取复合指标的组件列表
func (ms *MetricService) GetCompositeComponents(chineseName string) ([]string, error) {
	config, err := ms.GetMetricConfig(chineseName)
	if err != nil {
		return nil, err
	}

	if !config.IsComposite {
		return nil, fmt.Errorf("指标'%s'不是复合指标", chineseName)
	}

	return config.Components, nil
}
