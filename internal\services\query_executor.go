package services

import (
	"context"
	"log"

	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"neoapi/internal/models"
	"neoapi/internal/utils"
)

// QueryExecutor 查询执行器
type QueryExecutor struct {
	driver neo4j.DriverWithContext
}

// NewQueryExecutor 创建新的查询执行器
func NewQueryExecutor(driver neo4j.DriverWithContext) *QueryExecutor {
	return &QueryExecutor{
		driver: driver,
	}
}

// ExecuteShipQuery 执行船舶查询
func (qe *QueryExecutor) ExecuteShipQuery(ctx context.Context, query string, params map[string]interface{}) ([]models.EntityResult, int, error) {
	session := qe.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, params)
		if err != nil {
			log.Printf("[ExecuteShipQuery] Cypher query failed: %v", err)
			return nil, err
		}

		var results []models.EntityResult
		for records.Next(ctx) {
			record := records.Record()
			
			name := utils.GetStringValue(record.Values[0])
			mmsi := utils.GetStringValue(record.Values[1])
			dwt := utils.GetFloatValue(record.Values[2])
			province := utils.GetStringValue(record.Values[3])
			cargoVolume := utils.GetFloatValue(record.Values[4])
			voyageCount := int(utils.GetFloatValue(record.Values[5]))
			loadRatio := utils.GetFloatValue(record.Values[6])

			// 构建属性映射
			properties := map[string]interface{}{
				"mmsi":     mmsi,
				"dwt":      dwt,
				"province": province,
			}

			// 构建指标
			metrics := models.EntityMetrics{
				CargoVolume: cargoVolume,
				VoyageCount: voyageCount,
				LoadRatio:   loadRatio,
			}

			results = append(results, models.EntityResult{
				Name:       name,
				Identifier: mmsi,
				EntityType: models.EntityTypeShip,
				Properties: properties,
				Metrics:    metrics,
			})
		}

		return results, records.Err()
	})

	if err != nil {
		return nil, 0, err
	}

	results := result.([]models.EntityResult)
	return results, len(results), nil
}

// ExecuteRouteQuery 执行航线查询
func (qe *QueryExecutor) ExecuteRouteQuery(ctx context.Context, query string, params map[string]interface{}) ([]models.EntityResult, int, error) {
	session := qe.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, params)
		if err != nil {
			log.Printf("[ExecuteRouteQuery] Cypher query failed: %v", err)
			return nil, err
		}

		var results []models.EntityResult
		for records.Next(ctx) {
			record := records.Record()
			
			name := utils.GetStringValue(record.Values[0])
			routeId := utils.GetStringValue(record.Values[1])
			distance := utils.GetFloatValue(record.Values[2])
			originPort := utils.GetStringValue(record.Values[3])
			destinationPort := utils.GetStringValue(record.Values[4])
			routeType := utils.GetStringValue(record.Values[5])
			totalCargo := utils.GetFloatValue(record.Values[6])
			shipCount := int(utils.GetFloatValue(record.Values[7]))
			voyageCount := int(utils.GetFloatValue(record.Values[8]))

			// 构建属性映射
			properties := map[string]interface{}{
				"route_id":         routeId,
				"distance_km":      distance,
				"origin_port":      originPort,
				"destination_port": destinationPort,
				"route_type":       routeType,
			}

			// 构建指标
			metrics := models.EntityMetrics{
				TotalCargo:  totalCargo,
				ShipCount:   shipCount,
				VoyageCount: voyageCount,
			}

			// 构建港口列表
			var ports []string
			if originPort != "" {
				ports = append(ports, originPort)
			}
			if destinationPort != "" && destinationPort != originPort {
				ports = append(ports, destinationPort)
			}

			results = append(results, models.EntityResult{
				Name:       name,
				Identifier: routeId,
				EntityType: models.EntityTypeShippingRoute,
				Properties: properties,
				Metrics:    metrics,
				Ports:      ports,
			})
		}

		return results, records.Err()
	})

	if err != nil {
		return nil, 0, err
	}

	results := result.([]models.EntityResult)
	return results, len(results), nil
}

// ExecutePortQuery 执行港口查询
func (qe *QueryExecutor) ExecutePortQuery(ctx context.Context, query string, params map[string]interface{}) ([]models.EntityResult, int, error) {
	session := qe.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, params)
		if err != nil {
			log.Printf("[ExecutePortQuery] Cypher query failed: %v", err)
			return nil, err
		}

		var results []models.EntityResult
		for records.Next(ctx) {
			record := records.Record()
			
			name := utils.GetStringValue(record.Values[0])
			province := utils.GetStringValue(record.Values[1])
			totalCargo := utils.GetFloatValue(record.Values[2])
			shipCount := int(utils.GetFloatValue(record.Values[3]))
			inCargo := utils.GetFloatValue(record.Values[4])

			// 构建属性映射
			properties := map[string]interface{}{
				"province":  province,
				"in_cargo":  inCargo,
				"out_cargo": totalCargo - inCargo,
			}

			// 构建指标
			metrics := models.EntityMetrics{
				CargoVolume: totalCargo,
				ShipCount:   shipCount,
			}

			results = append(results, models.EntityResult{
				Name:       name,
				Identifier: name, // 港口使用名称作为标识符
				EntityType: models.EntityTypePort,
				Properties: properties,
				Metrics:    metrics,
			})
		}

		return results, records.Err()
	})

	if err != nil {
		return nil, 0, err
	}

	results := result.([]models.EntityResult)
	return results, len(results), nil
}
