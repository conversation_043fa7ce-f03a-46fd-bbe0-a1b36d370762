package services

import (
	"context"
	"fmt"
	"log"
	"neoapi/internal/models"
	timeparser "neoapi/internal/time"
	"neoapi/internal/utils"

	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
)

// executePointQuery 执行单点查询
func (s *QueryService) executePointQuery(ctx context.Context, entity *models.EntityInfo, metric string, timeQuery *timeparser.TimeQuery, timeExpression string) (interface{}, error) {
	if len(timeQuery.MonthNodes) == 0 {
		return nil, fmt.Errorf("no time periods specified for point query")
	}

	// 使用第一个时间节点进行单点查询
	period := timeQuery.MonthNodes[0]
	dbField := s.mapMetricToDBField(metric)

	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	var query string
	var params map[string]interface{}

	switch entity.Type {
	case models.EntityTypeShip:
		query = fmt.Sprintf(`
			MATCH (s:Ship {name: $entityName})<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
			RETURN sms.%s as value, ym.daysInMonth as totalDays
		`, dbField)
		params = map[string]interface{}{
			"entityName": entity.Name,
			"period":     period,
		}
	case models.EntityTypePort:
		query = fmt.Sprintf(`
			MATCH (p:Port {name: $entityName})<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
			RETURN pms.%s as value, ym.daysInMonth as totalDays
		`, dbField)
		params = map[string]interface{}{
			"entityName": entity.Name,
			"period":     period,
		}
	case models.EntityTypeShippingRoute:
		query = fmt.Sprintf(`
			MATCH (sr:ShippingRoute {routeName: $entityName})<-[:ROUTE_STAT_FOR_ROUTE]-(rms:RouteMonthStat)-[:ROUTE_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
			RETURN rms.%s as value, rms.totalShipCount as totalShips, rms.totalVoyageCount as totalVoyages
		`, dbField)
		params = map[string]interface{}{
			"entityName": entity.RouteName,
			"period":     period,
		}
	default:
		return nil, fmt.Errorf("unsupported entity type for point query: %s", entity.Type)
	}

	// 打印Cypher查询日志
	log.Printf("[executePointQuery] Executing Cypher query:\n%s\nParams: %+v", query, params)

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, params)
		if err != nil {
			log.Printf("[executePointQuery] Cypher query failed: %v", err)
			return nil, err
		}

		if !records.Next(ctx) {
			return nil, nil // 返回nil，让外层处理降级
		}

		record := records.Record()
		value := record.Values[0]
		unit := s.getMetricUnit(metric)

		return &models.PointQueryData{
			Value: value,
			Unit:  unit,
		}, nil
	})

	if err != nil || result == nil {
		// 使用统一降级处理
		fallbackResult := UnifiedFallback(ctx, s.driver, entity.Type, entity.Name, metric, timeExpression)
		unit := s.getMetricUnit(metric)
		return &models.PointQueryData{
			Value: fallbackResult.Value,
			Unit:  unit,
		}, nil
	}

	return result, nil
}

// executeTrendQuery 执行趋势查询
func (s *QueryService) executeTrendQuery(ctx context.Context, entity *models.EntityInfo, metric string, timeQuery *timeparser.TimeQuery) (interface{}, error) {
	if len(timeQuery.MonthNodes) < 2 {
		return nil, fmt.Errorf("trend query requires at least 2 time periods")
	}

	dbField := s.mapMetricToDBField(metric)
	unit := s.getMetricUnit(metric)

	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	var query string
	var params map[string]interface{}

	switch entity.Type {
	case models.EntityTypeShip:
		query = fmt.Sprintf(`
			MATCH (s:Ship {name: $entityName})<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth)
			WHERE ym.ym IN $periods
			RETURN ym.ym as period, sms.%s as value
			ORDER BY ym.ym
		`, dbField)
		params = map[string]interface{}{
			"entityName": entity.Name,
			"periods":    timeQuery.MonthNodes,
		}
	case models.EntityTypePort:
		query = fmt.Sprintf(`
			MATCH (p:Port {name: $entityName})<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth)
			WHERE ym.ym IN $periods
			RETURN ym.ym as period, pms.%s as value
			ORDER BY ym.ym
		`, dbField)
		params = map[string]interface{}{
			"entityName": entity.Name,
			"periods":    timeQuery.MonthNodes,
		}
	case models.EntityTypeShippingRoute:
		query = fmt.Sprintf(`
			MATCH (sr:ShippingRoute {routeName: $entityName})<-[:ROUTE_STAT_FOR_ROUTE]-(rms:RouteMonthStat)-[:ROUTE_STAT_FOR_MONTH]->(ym:YearMonth)
			WHERE ym.ym IN $periods
			RETURN ym.ym as period, rms.%s as value
			ORDER BY ym.ym
		`, dbField)
		params = map[string]interface{}{
			"entityName": entity.RouteName,
			"periods":    timeQuery.MonthNodes,
		}
	default:
		return nil, fmt.Errorf("unsupported entity type for trend query: %s", entity.Type)
	}

	// 打印Cypher查询日志
	log.Printf("[executeTrendQuery] Executing Cypher query:\n%s\nParams: %+v", query, params)

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, params)
		if err != nil {
			log.Printf("[executeTrendQuery] Cypher query failed: %v", err)
			return nil, err
		}

		var trendData []models.TrendDataPoint
		var values []float64

		for records.Next(ctx) {
			record := records.Record()
			period := record.Values[0].(string)
			value := record.Values[1]

			trendData = append(trendData, models.TrendDataPoint{
				Period: period,
				Value:  value,
				Unit:   unit,
			})

			if floatVal := utils.GetFloatValue(value); floatVal != 0 {
				values = append(values, floatVal)
			}
		}

		if err := records.Err(); err != nil {
			return nil, err
		}

		// 获取指标单位
		unit := s.getMetricUnit(metric)
		return &models.TrendQueryData{
			Trend: trendData,
			Unit:  unit,
		}, nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// executeCompareQuery 执行对比查询
func (s *QueryService) executeCompareQuery(ctx context.Context, entityReq *models.EntityRequest, metric string, timeQuery *timeparser.TimeQuery, timeExpression string) (interface{}, error) {
	// 处理多实体标识符
	identifiers, ok := entityReq.Identifier.([]interface{})
	if !ok {
		return nil, fmt.Errorf("compare query requires multiple entity identifiers")
	}

	if len(timeQuery.MonthNodes) == 0 {
		return nil, fmt.Errorf("no time periods specified for compare query")
	}

	dbField := s.mapMetricToDBField(metric)
	unit := s.getMetricUnit(metric)

	// 判断是单个时间点还是时间范围
	isSinglePeriod := len(timeQuery.MonthNodes) == 1
	log.Printf("[executeCompareQuery] Time periods: %v, isSinglePeriod: %v", timeQuery.MonthNodes, isSinglePeriod)

	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	var comparison []models.ComparisonDataPoint

	for _, identifier := range identifiers {
		entityName, ok := identifier.(string)
		if !ok {
			continue
		}

		var query string
		var params map[string]interface{}

		if isSinglePeriod {
			// 单个时间点查询
			period := timeQuery.MonthNodes[0]
			switch entityReq.Type {
			case models.EntityTypeShip:
				query = fmt.Sprintf(`
					MATCH (s:Ship {name: $entityName})<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
					RETURN sms.%s as value
				`, dbField)
			case models.EntityTypePort:
				query = fmt.Sprintf(`
					MATCH (p:Port {name: $entityName})<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
					RETURN pms.%s as value
				`, dbField)
			case models.EntityTypeShippingRoute:
				query = fmt.Sprintf(`
					MATCH (sr:ShippingRoute {routeName: $entityName})<-[:ROUTE_STAT_FOR_ROUTE]-(rms:RouteMonthStat)-[:ROUTE_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
					RETURN rms.%s as value
				`, dbField)
			default:
				continue
			}
			params = map[string]interface{}{
				"entityName": entityName,
				"period":     period,
			}
		} else {
			// 时间范围查询 - 返回明细数据和聚合值
			switch entityReq.Type {
			case models.EntityTypeShip:
				query = fmt.Sprintf(`
					MATCH (s:Ship {name: $entityName})<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth)
					WHERE ym.ym IN $periods
					RETURN ym.ym as period, sms.%s as value
					ORDER BY ym.ym
				`, dbField)
			case models.EntityTypePort:
				query = fmt.Sprintf(`
					MATCH (p:Port {name: $entityName})<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth)
					WHERE ym.ym IN $periods
					RETURN ym.ym as period, pms.%s as value
					ORDER BY ym.ym
				`, dbField)
			case models.EntityTypeShippingRoute:
				query = fmt.Sprintf(`
					MATCH (sr:ShippingRoute {routeName: $entityName})<-[:ROUTE_STAT_FOR_ROUTE]-(rms:RouteMonthStat)-[:ROUTE_STAT_FOR_MONTH]->(ym:YearMonth)
					WHERE ym.ym IN $periods
					RETURN ym.ym as period, rms.%s as value
					ORDER BY ym.ym
				`, dbField)
			default:
				continue
			}
			params = map[string]interface{}{
				"entityName": entityName,
				"periods":    timeQuery.MonthNodes,
			}
		}

		// 打印Cypher查询日志
		log.Printf("[executeCompareQuery] Executing Cypher query for entity '%s':\n%s\nParams: %+v", entityName, query, params)

		result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
			records, err := tx.Run(ctx, query, params)
			if err != nil {
				log.Printf("[executeCompareQuery] Cypher query failed for entity '%s': %v", entityName, err)
				return nil, err
			}

			if isSinglePeriod {
				// 单个时间点 - 返回单个值
				if !records.Next(ctx) {
					return nil, nil // 没有数据
				}
				return records.Record().Values[0], nil
			} else {
				// 时间范围 - 返回明细数据和统计摘要
				var details []models.TrendDataPoint
				var values []float64

				for records.Next(ctx) {
					record := records.Record()
					period := record.Values[0].(string)
					value := record.Values[1]

					details = append(details, models.TrendDataPoint{
						Period: period,
						Value:  value,
						Unit:   unit,
					})

					if floatVal := utils.GetFloatValue(value); floatVal > 0 {
						values = append(values, floatVal)
					}
				}

				if len(details) == 0 {
					return nil, nil // 没有数据
				}

				// 计算统计摘要
				summary := calculateSummary(values)

				return map[string]interface{}{
					"details": details,
					"summary": summary,
					"total":   summary.Total,
				}, nil
			}
		})

		if err != nil || result == nil {
			// 使用统一降级处理
			fallbackResult := UnifiedFallback(ctx, s.driver, entityReq.Type, entityName, metric, timeExpression)
			comparison = append(comparison, models.ComparisonDataPoint{
				Entity: entityName,
				Value:  fallbackResult.Value,
			})
			continue
		}

		// 处理正常值情况
		if isSinglePeriod {
			comparison = append(comparison, models.ComparisonDataPoint{
				Entity: entityName,
				Value:  result,
			})
		} else {
			// 时间范围结果
			resultMap := result.(map[string]interface{})
			comparison = append(comparison, models.ComparisonDataPoint{
				Entity:  entityName,
				Value:   resultMap["total"],
				Details: resultMap["details"].([]models.TrendDataPoint),
				Summary: resultMap["summary"].(*models.ComparisonSummary),
			})
		}
	}

	return &models.CompareQueryData{
		Comparison: comparison,
		Unit:       unit,
	}, nil
}

// executeRankQuery 执行排名查询 (已废弃，请使用 /api/v3.1/query/entities 接口)
// Deprecated: 使用 EntityQueryService.QueryEntities 替代
func (s *QueryService) executeRankQuery(ctx context.Context, entityReq *models.EntityRequest, metric string, timeQuery *timeparser.TimeQuery, limit int) (interface{}, error) {
	if len(timeQuery.MonthNodes) == 0 {
		return nil, fmt.Errorf("no time periods specified for rank query")
	}

	period := timeQuery.MonthNodes[0]
	dbField := s.mapMetricToDBField(metric)
	unit := s.getMetricUnit(metric)

	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	var query string
	var params map[string]interface{}

	// 设置limit参数，默认10，最大100
	if limit <= 0 {
		limit = 10
	} else if limit > 100 {
		limit = 100
	}

	switch entityReq.Type {
	case models.EntityTypeShip:
		query = fmt.Sprintf(`
			MATCH (s:Ship)<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
			WHERE sms.%s IS NOT NULL
			RETURN s.name as entityName, sms.%s as value
			ORDER BY sms.%s DESC
			LIMIT $limit
		`, dbField, dbField, dbField)
	case models.EntityTypePort:
		query = fmt.Sprintf(`
			MATCH (p:Port)<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
			WHERE pms.%s IS NOT NULL
			RETURN p.name as entityName, pms.%s as value
			ORDER BY pms.%s DESC
			LIMIT $limit
		`, dbField, dbField, dbField)
	case models.EntityTypeShippingRoute:
		query = fmt.Sprintf(`
			MATCH (sr:ShippingRoute)<-[:ROUTE_STAT_FOR_ROUTE]-(rms:RouteMonthStat)-[:ROUTE_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
			WHERE rms.%s IS NOT NULL
			RETURN sr.routeName as entityName, rms.%s as value
			ORDER BY rms.%s DESC
			LIMIT $limit
		`, dbField, dbField, dbField)
	default:
		return nil, fmt.Errorf("unsupported entity type for rank query: %s", entityReq.Type)
	}

	params = map[string]interface{}{
		"period": period,
		"limit":  limit,
	}

	// 打印Cypher查询日志
	log.Printf("[executeRankQuery] Executing Cypher query:\n%s\nParams: %+v", query, params)

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, params)
		if err != nil {
			log.Printf("[executeRankQuery] Cypher query failed: %v", err)
			return nil, err
		}

		var ranking []models.RankingDataPoint
		rank := 1

		for records.Next(ctx) {
			record := records.Record()
			entityName := record.Values[0].(string)
			value := record.Values[1]

			ranking = append(ranking, models.RankingDataPoint{
				Rank:       rank,
				EntityName: entityName,
				Value:      value,
				Unit:       unit,
			})
			rank++
		}

		unit := s.getMetricUnit(metric)
		return &models.RankQueryData{
			Ranking: ranking,
			Unit:    unit,
		}, records.Err()
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// executeComposeQuery 执行构成查询
func (s *QueryService) executeComposeQuery(ctx context.Context, entity *models.EntityInfo, metric string, timeQuery *timeparser.TimeQuery, options interface{}) (interface{}, error) {
	if len(timeQuery.MonthNodes) == 0 {
		return nil, fmt.Errorf("no time periods specified for compose query")
	}

	period := timeQuery.MonthNodes[0]

	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	var query string
	var params map[string]interface{}

	// 根据实体类型和指标确定构成查询
	if metric == "货物构成" {
		switch entity.Type {
		case models.EntityTypeShip:
			query = `
				MATCH (s:Ship {name: $entityName})<-[:CARGO_STAT_FOR_SHIP]-(smcs:ShipMonthCargoStat)-[:CARGO_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
				MATCH (smcs)-[:CARGO_STAT_FOR_TYPE]->(ct:CargoType)
				RETURN ct.subName as name, smcs.cargo_ton as value
				ORDER BY smcs.cargo_ton DESC
			`
		case models.EntityTypePort:
			query = `
				MATCH (p:Port {name: $entityName})<-[:PORT_CARGO_STAT_FOR_PORT]-(pmcs:PortMonthCargoStat)-[:PORT_CARGO_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
				MATCH (pmcs)-[:PORT_CARGO_STAT_FOR_TYPE]->(ct:CargoType)
				RETURN ct.subName as name, (pmcs.inCargo_ton + pmcs.outCargo_ton) as value
				ORDER BY value DESC
			`
		case models.EntityTypeShippingRoute:
			query = `
				MATCH (sr:ShippingRoute {routeName: $entityName})<-[:ROUTE_CARGO_STAT_FOR_ROUTE]-(rmcs:RouteMonthCargoStat)-[:ROUTE_CARGO_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
				MATCH (rmcs)-[:ROUTE_CARGO_STAT_FOR_TYPE]->(ct:CargoType)
				RETURN ct.subName as name, rmcs.cargo_ton as value
				ORDER BY rmcs.cargo_ton DESC
			`
		default:
			return nil, fmt.Errorf("unsupported entity type for cargo composition: %s", entity.Type)
		}

		params = map[string]interface{}{
			"entityName": entity.Name,
			"period":     period,
		}
	} else {
		return nil, fmt.Errorf("unsupported composition metric: %s", metric)
	}

	// 打印Cypher查询日志
	log.Printf("[executeComposeQuery] Executing Cypher query:\n%s\nParams: %+v", query, params)

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, params)
		if err != nil {
			log.Printf("[executeComposeQuery] Cypher query failed: %v", err)
			return nil, err
		}

		var composition []models.CompositionDataPoint
		var total float64

		for records.Next(ctx) {
			record := records.Record()
			name := record.Values[0].(string)
			value := utils.GetFloatValue(record.Values[1])

			composition = append(composition, models.CompositionDataPoint{
				Name:  name,
				Value: value,
				Unit:  "吨",
			})
			total += value
		}

		// 计算百分比
		for i := range composition {
			if total > 0 {
				composition[i].Percentage = composition[i].Value / total * 100
			}
		}

		return &models.ComposeQueryData{
			Composition: composition,
			Total:       total,
			Unit:        "吨", // 构成查询通常是货物吨数
		}, records.Err()
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// 辅助函数
func getDataSourceForEntity(entityType models.EntityType) string {
	switch entityType {
	case models.EntityTypeShip:
		return "ShipMonthStat"
	case models.EntityTypePort:
		return "PortMonthStat"
	case models.EntityTypeShippingRoute:
		return "RouteMonthStat"
	default:
		return "Unknown"
	}
}

func calculateTrendAnalysis(values []float64) *models.TrendAnalysis {
	if len(values) < 2 {
		return nil
	}

	// 简单的趋势分析
	first := values[0]
	last := values[len(values)-1]

	var direction string
	var growthRate float64

	if last > first {
		direction = "上升"
		if first != 0 {
			growthRate = (last - first) / first
		}
	} else if last < first {
		direction = "下降"
		if first != 0 {
			growthRate = (last - first) / first
		}
	} else {
		direction = "平稳"
		growthRate = 0
	}

	// 计算波动性
	volatility := "低"
	if len(values) > 2 {
		// 简单的波动性计算
		var variance float64
		mean := (first + last) / 2
		for _, v := range values {
			variance += (v - mean) * (v - mean)
		}
		variance /= float64(len(values))

		if variance > mean*0.1 {
			volatility = "高"
		} else if variance > mean*0.05 {
			volatility = "中等"
		}
	}

	return &models.TrendAnalysis{
		TrendDirection: direction,
		GrowthRate:     growthRate,
		Volatility:     volatility,
	}
}

// calculateSummary 计算数值数组的统计摘要
func calculateSummary(values []float64) *models.ComparisonSummary {
	if len(values) == 0 {
		return &models.ComparisonSummary{
			Total:   0,
			Average: 0,
			Max:     0,
			Min:     0,
			Count:   0,
		}
	}

	var total, max, min float64
	total = 0
	max = values[0]
	min = values[0]

	for _, value := range values {
		total += value
		if value > max {
			max = value
		}
		if value < min {
			min = value
		}
	}

	average := total / float64(len(values))

	return &models.ComparisonSummary{
		Total:   total,
		Average: average,
		Max:     max,
		Min:     min,
		Count:   len(values),
	}
}

// executeFilteredDimensionQuery 执行过滤维度查询（降级处理）
func (s *QueryService) executeFilteredDimensionQuery(ctx context.Context, entity *models.EntityInfo, filters interface{}, timeQuery *timeparser.TimeQuery) (interface{}, error) {
	if len(timeQuery.MonthNodes) == 0 {
		return nil, fmt.Errorf("no time periods specified for filtered dimension query")
	}

	filterMap, ok := filters.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid filters format")
	}

	log.Printf("[executeFilteredDimensionQuery] Processing filters: %+v for entity: %s, periods: %v", filterMap, entity.Name, timeQuery.MonthNodes)

	// 根据过滤器类型决定查询维度
	if cargoType, hasCargoFilter := filterMap["cargo_type"]; hasCargoFilter {
		log.Printf("[executeFilteredDimensionQuery] Executing cargo type dimension query for: %s", cargoType)
		cargoTypeStr, ok := cargoType.(string)
		if !ok {
			return nil, fmt.Errorf("cargo_type filter must be a string")
		}
		return s.queryCargoTypeDimension(ctx, entity, cargoTypeStr, timeQuery)
	}

	if shipType, hasShipFilter := filterMap["ship_type"]; hasShipFilter {
		log.Printf("[executeFilteredDimensionQuery] Executing ship type dimension query for: %s", shipType)
		// TODO: 实现 queryShipTypeDimension 函数
		return map[string]interface{}{
			"message": "Ship type dimension query not yet implemented",
			"filter":  shipType,
		}, nil
	}

	if route, hasRouteFilter := filterMap["route"]; hasRouteFilter {
		log.Printf("[executeFilteredDimensionQuery] Executing route dimension query for: %s", route)
		// TODO: 实现 queryRouteDimension 函数
		return map[string]interface{}{
			"message": "Route dimension query not yet implemented",
			"filter":  route,
		}, nil
	}

	return nil, fmt.Errorf("unsupported filter type, supported filters: cargo_type, ship_type, route")
}

// queryCargoTypeDimension 查询货物类型维度的所有指标
func (s *QueryService) queryCargoTypeDimension(ctx context.Context, entity *models.EntityInfo, cargoType string, timeQuery *timeparser.TimeQuery) (interface{}, error) {
	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	var query string
	var params map[string]interface{}

	// 处理多个时间节点
	periods := timeQuery.MonthNodes

	switch entity.Type {
	case models.EntityTypeShip:
		query = `
			MATCH (s:Ship {name: $entityName})<-[:CARGO_STAT_FOR_SHIP]-(smcs:ShipMonthCargoStat)-[:CARGO_STAT_FOR_MONTH]->(ym:YearMonth)
			WHERE ym.ym IN $periods
			MATCH (smcs)-[:CARGO_STAT_FOR_TYPE]->(ct:CargoType {subName: $cargoType})
			RETURN ym.ym as period, {
				cargo_ton: smcs.cargo_ton,
				voyage_count: smcs.voyage_count,
				avg_load_ratio: smcs.avg_load_ratio,
				total_distance: smcs.total_distance
			} as metrics
			ORDER BY ym.ym
		`
		params = map[string]interface{}{
			"entityName": entity.Name,
			"cargoType":  cargoType,
			"periods":    periods,
		}
	case models.EntityTypePort:
		query = `
			MATCH (p:Port {name: $entityName})<-[:PORT_CARGO_STAT_FOR_PORT]-(pmcs:PortMonthCargoStat)-[:PORT_CARGO_STAT_FOR_MONTH]->(ym:YearMonth)
			WHERE ym.ym IN $periods
			MATCH (pmcs)-[:PORT_CARGO_STAT_FOR_TYPE]->(ct:CargoType {subName: $cargoType})
			RETURN ym.ym as period, {
				inCargo_ton: pmcs.inCargo_ton,
				outCargo_ton: pmcs.outCargo_ton,
				totalCargo_ton: pmcs.inCargo_ton + pmcs.outCargo_ton,
				ship_count: pmcs.ship_count
			} as metrics
			ORDER BY ym.ym
		`
		params = map[string]interface{}{
			"entityName": entity.Name,
			"cargoType":  cargoType,
			"periods":    periods,
		}
	case models.EntityTypeShippingRoute:
		query = `
			MATCH (sr:ShippingRoute {routeName: $entityName})<-[:ROUTE_CARGO_STAT_FOR_ROUTE]-(rmcs:RouteMonthCargoStat)-[:ROUTE_CARGO_STAT_FOR_MONTH]->(ym:YearMonth)
			WHERE ym.ym IN $periods
			MATCH (rmcs)-[:ROUTE_CARGO_STAT_FOR_TYPE]->(ct:CargoType {subName: $cargoType})
			RETURN ym.ym as period, {
				cargo_ton: rmcs.cargo_ton,
				ship_count: rmcs.ship_count,
				voyage_count: rmcs.voyage_count,
				avg_load_ratio: rmcs.avg_load_ratio
			} as metrics
			ORDER BY ym.ym
		`
		params = map[string]interface{}{
			"entityName": entity.RouteName,
			"cargoType":  cargoType,
			"periods":    periods,
		}
	default:
		return nil, fmt.Errorf("unsupported entity type for cargo type dimension query: %s", entity.Type)
	}

	// 打印Cypher查询日志
	log.Printf("[queryCargoTypeDimension] Executing Cypher query:\n%s\nParams: %+v", query, params)

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, params)
		if err != nil {
			log.Printf("[queryCargoTypeDimension] Cypher query failed: %v", err)
			return nil, err
		}

		// 收集所有时间节点的数据
		var dataPoints []map[string]interface{}
		for records.Next(ctx) {
			record := records.Record()
			period := record.Values[0].(string)
			metrics := record.Values[1]

			dataPoints = append(dataPoints, map[string]interface{}{
				"period":  period,
				"metrics": metrics,
			})
		}

		if len(dataPoints) == 0 {
			log.Printf("[queryCargoTypeDimension] No data found for entity: %s, cargo type: %s, periods: %v", entity.Name, cargoType, periods)
			return []map[string]interface{}{}, nil
		}

		log.Printf("[queryCargoTypeDimension] Found %d data points for entity: %s, cargo type: %s", len(dataPoints), entity.Name, cargoType)
		return dataPoints, nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}
