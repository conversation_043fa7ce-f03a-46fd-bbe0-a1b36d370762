package services

import (
	"context"
	"testing"

	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
	"neoapi/internal/models"
)

// MockDriver 模拟Neo4j驱动（导出供其他包使用）
type MockDriver struct{}

func (m *MockDriver) NewSession(ctx context.Context, config neo4j.SessionConfig) neo4j.SessionWithContext {
	return &MockSession{}
}

func (m *MockDriver) VerifyConnectivity(ctx context.Context) error {
	return nil
}

func (m *MockDriver) Close(ctx context.Context) error {
	return nil
}

func (m *MockDriver) Target() neo4j.ServerInfo {
	return neo4j.ServerInfo{}
}

func (m *MockDriver) IsEncrypted() bool {
	return false
}

// MockSession 模拟Neo4j会话
type MockSession struct{}

func (m *MockSession) ExecuteRead(ctx context.Context, work neo4j.ManagedTransactionWork, configurers ...func(*neo4j.TransactionConfig)) (interface{}, error) {
	return work(&MockTransaction{})
}

func (m *MockSession) ExecuteWrite(ctx context.Context, work neo4j.ManagedTransactionWork, configurers ...func(*neo4j.TransactionConfig)) (interface{}, error) {
	return work(&MockTransaction{})
}

func (m *MockSession) Close(ctx context.Context) error {
	return nil
}

func (m *MockSession) LastBookmarks() neo4j.Bookmarks {
	return nil
}

func (m *MockSession) BeginTransaction(ctx context.Context, configurers ...func(*neo4j.TransactionConfig)) (neo4j.ExplicitTransaction, error) {
	return nil, nil
}

// MockTransaction 模拟Neo4j事务
type MockTransaction struct{}

func (m *MockTransaction) Run(ctx context.Context, cypher string, parameters map[string]interface{}) (neo4j.ResultWithContext, error) {
	return &MockResult{}, nil
}

func (m *MockTransaction) Commit(ctx context.Context) error {
	return nil
}

func (m *MockTransaction) Rollback(ctx context.Context) error {
	return nil
}

func (m *MockTransaction) Close(ctx context.Context) error {
	return nil
}

// MockResult 模拟Neo4j结果
type MockResult struct {
	records []neo4j.Record
	index   int
}

func (m *MockResult) Next(ctx context.Context) bool {
	return m.index < len(m.records)
}

func (m *MockResult) NextRecord(ctx context.Context, record **neo4j.Record) bool {
	if m.index < len(m.records) {
		*record = &m.records[m.index]
		m.index++
		return true
	}
	return false
}

func (m *MockResult) PeekRecord(ctx context.Context, record **neo4j.Record) bool {
	if m.index < len(m.records) {
		*record = &m.records[m.index]
		return true
	}
	return false
}

func (m *MockResult) Record() *neo4j.Record {
	if m.index > 0 && m.index <= len(m.records) {
		return &m.records[m.index-1]
	}
	return nil
}

func (m *MockResult) Err() error {
	return nil
}

func (m *MockResult) Consume(ctx context.Context) (neo4j.ResultSummary, error) {
	return nil, nil
}

func (m *MockResult) Keys() ([]string, error) {
	return []string{}, nil
}

func (m *MockResult) IsOpen() bool {
	return true
}

func (m *MockResult) Buffer(ctx context.Context) error {
	return nil
}

func TestQueryService_ExecuteQuery(t *testing.T) {
	// 创建模拟驱动
	mockDriver := &MockDriver{}
	
	// 创建查询服务
	service := NewQueryService(mockDriver)
	
	// 测试用例
	tests := []struct {
		name    string
		request *models.UnifiedQueryRequest
		wantErr bool
	}{
		{
			name: "有效的船舶画像查询",
			request: &models.UnifiedQueryRequest{
				QueryType: models.QueryTypeProfile,
				Entity: models.EntityRequest{
					Type:               models.EntityTypeShip,
					Identifier:         "测试船舶",
					ResolutionStrategy: models.ResolutionFuzzy,
				},
				Metric:         "船舶画像",
				TimeExpression: "R1M",
				Options: models.QueryOptions{
					DetailLevel: "basic",
				},
			},
			wantErr: false,
		},
		{
			name: "无效的时间表达式",
			request: &models.UnifiedQueryRequest{
				QueryType: models.QueryTypePoint,
				Entity: models.EntityRequest{
					Type:       models.EntityTypeShip,
					Identifier: "测试船舶",
				},
				Metric:         "货运量",
				TimeExpression: "INVALID_TIME",
			},
			wantErr: true,
		},
		{
			name: "不支持的查询类型",
			request: &models.UnifiedQueryRequest{
				QueryType: "UNSUPPORTED",
				Entity: models.EntityRequest{
					Type:       models.EntityTypeShip,
					Identifier: "测试船舶",
				},
				Metric:         "货运量",
				TimeExpression: "R1M",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			response, err := service.ExecuteQuery(context.Background(), tt.request)
			
			if tt.wantErr {
				if err == nil && response.Status != "error" {
					t.Errorf("ExecuteQuery() expected error but got none")
				}
				return
			}
			
			if err != nil {
				t.Errorf("ExecuteQuery() unexpected error: %v", err)
				return
			}
			
			if response == nil {
				t.Errorf("ExecuteQuery() returned nil response")
				return
			}
			
			if response.Status != "success" && response.Status != "error" {
				t.Errorf("ExecuteQuery() invalid status: %s", response.Status)
			}
			
			// 检查查询元数据
			if response.QueryMeta.QueryType != tt.request.QueryType {
				t.Errorf("ExecuteQuery() query type mismatch: got %s, want %s", 
					response.QueryMeta.QueryType, tt.request.QueryType)
			}
			
			// 检查性能信息
			if response.Performance == nil {
				t.Errorf("ExecuteQuery() missing performance info")
			}
		})
	}
}

func TestQueryService_MapMetricToDBField(t *testing.T) {
	service := NewQueryService(&MockDriver{})
	
	tests := []struct {
		metric   string
		expected string
	}{
		{"有效营运率", "opRatio"},
		{"航次数", "voyages"},
		{"负载率", "loadRatio"},
		{"货运量", "capacity_ton"},
		{"周转量", "turnover_tonkm"},
		{"未知指标", "未知指标"},
	}
	
	for _, tt := range tests {
		t.Run(tt.metric, func(t *testing.T) {
			result := service.mapMetricToDBField(tt.metric)
			if result != tt.expected {
				t.Errorf("mapMetricToDBField(%s) = %s, want %s", tt.metric, result, tt.expected)
			}
		})
	}
}

func TestQueryService_GetMetricUnit(t *testing.T) {
	service := NewQueryService(&MockDriver{})
	
	tests := []struct {
		metric   string
		expected string
	}{
		{"有效营运率", "比率"},
		{"航次数", "次"},
		{"货运量", "吨"},
		{"周转量", "吨公里"},
		{"未知指标", ""},
	}
	
	for _, tt := range tests {
		t.Run(tt.metric, func(t *testing.T) {
			result := service.getMetricUnit(tt.metric)
			if result != tt.expected {
				t.Errorf("getMetricUnit(%s) = %s, want %s", tt.metric, result, tt.expected)
			}
		})
	}
}
