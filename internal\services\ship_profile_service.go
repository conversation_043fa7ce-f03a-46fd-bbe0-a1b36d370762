package services

import (
	"context"
	"fmt"
	"log"
	"sort"
	"time"
	"neoapi/internal/utils"
	"neoapi/internal/models"
	timeparser "neoapi/internal/time"

	"github.com/neo4j/neo4j-go-driver/v5/neo4j"
)

// executeProfileQuery 执行画像查询
func (s *QueryService) executeProfileQuery(ctx context.Context, entity *models.EntityInfo, timeQuery *timeparser.TimeQuery, options interface{}) (interface{}, error) {
	switch entity.Type {
	case models.EntityTypeShip:
		return s.executeShipProfileQuery(ctx, entity, timeQuery, nil)
	case models.EntityTypePort:
		return s.executePortProfileQuery(ctx, entity, timeQuery, nil)
	case models.EntityTypeShippingRoute:
		// 使用专门的RouteService处理航线画像查询
		// 优先使用RouteName，如果为空则使用RouteID
		routeIdentifier := entity.RouteName
		if routeIdentifier == "" {
			routeIdentifier = entity.RouteID
		}
		log.Printf("[executeProfileQuery] Using route identifier: '%s' (RouteName: '%s', RouteID: '%s')", routeIdentifier, entity.RouteName, entity.RouteID)
		return s.routeService.GetRouteProfile(ctx, routeIdentifier, timeQuery, true, true, true)
	default:
		return nil, fmt.Errorf("unsupported entity type for profile query: %s", entity.Type)
	}
}

// executeShipProfileQuery 执行船舶画像查询
func (s *QueryService) executeShipProfileQuery(ctx context.Context, entity *models.EntityInfo, timeQuery *timeparser.TimeQuery, options interface{}) (*models.ShipProfileData, error) {
	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	// 1. 获取船舶基本信息
	basicInfo, err := s.getShipBasicInfo(ctx, session, entity.Name)
	if err != nil {
		return nil, fmt.Errorf("failed to get ship basic info: %w", err)
	}

	// 2. 获取船舶类型信息
	shipType, err := s.getShipTypeInfo(ctx, session, entity.Name)
	if err != nil {
		return nil, fmt.Errorf("failed to get ship type info: %w", err)
	}

	// 3. 获取最近统计数据
	recentStats, err := s.getShipRecentStats(ctx, session, entity.Name)
	if err != nil {
		return nil, fmt.Errorf("failed to get ship recent stats: %w", err)
	}

	// 4. 获取历史表现数据（默认12个月）
	historicalPerformance, _ := s.getShipHistoricalPerformance(ctx, session, entity.Name, 12)

	// 5. 获取货物构成
	cargoComposition, _ := s.getShipCargoComposition(ctx, session, entity.Name, 12)

	// 6. 获取航线分析
	routeAnalysis, _ := s.getShipRouteAnalysis(ctx, session, entity.Name, 12)

	// 7. 获取关系信息
	relationships, _ := s.getShipRelationships(ctx, session, entity.Name)

	// 8. 获取实时状态
	realtimeStatus, _ := s.getShipRealtimeStatus(ctx, session, entity.Name)

	// 9. 生成摘要
	summary := s.generateShipProfileSummary(historicalPerformance, cargoComposition, routeAnalysis)

	return &models.ShipProfileData{
		BasicInfo:             *basicInfo,
		ShipType:              *shipType,
		RecentStats:           *recentStats,
		HistoricalPerformance: historicalPerformance,
		CargoComposition:      cargoComposition,

		RouteAnalysis:  routeAnalysis,
		Relationships:  *relationships,
		RealtimeStatus: realtimeStatus,
		Summary:        *summary,
	}, nil
}

// getShipBasicInfo 获取船舶基本信息
func (s *QueryService) getShipBasicInfo(ctx context.Context, session neo4j.SessionWithContext, shipName string) (*models.ShipBasicInfo, error) {
	query := `
		MATCH (s:Ship {name: $shipName})
		RETURN s.mmsi as mmsi, s.name as name, s.owner as owner, s.operator as operator,
		       s.dwt as dwt, s.grossTon as grossTon, s.builtDate as builtDate,
		       s.regPortName as regPortName, s.regPortProvince as regPortProvince,
		       s.navAreaType as navAreaType
	`

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		params := map[string]interface{}{"shipName": shipName}
		log.Printf("[getShipBasicInfo] Executing Cypher query:\n%s\nParams: %+v", query, params)

		records, err := tx.Run(ctx, query, params)
		if err != nil {
			return nil, err
		}

		if !records.Next(ctx) {
			return nil, fmt.Errorf("ship not found: %s", shipName)
		}

		record := records.Record()
		basicInfo := &models.ShipBasicInfo{
			MMSI:            utils.GetStringValue(record.Values[0]),
			Name:            utils.GetStringValue(record.Values[1]),
			Owner:           utils.GetStringValue(record.Values[2]),
			Operator:        utils.GetStringValue(record.Values[3]),
			DWT:             utils.GetFloatValue(record.Values[4]),
			GrossTon:        utils.GetFloatValue(record.Values[5]),
			RegPortName:     utils.GetStringValue(record.Values[7]),
			RegPortProvince: utils.GetStringValue(record.Values[8]),
			NavAreaType:     utils.GetStringValue(record.Values[9]),
		}

		// 处理建成日期
		if record.Values[6] != nil {
			if dateVal, ok := record.Values[6].(time.Time); ok {
				basicInfo.BuiltDate = dateVal
			}
		}

		return basicInfo, nil
	})

	if err != nil {
		return nil, err
	}

	return result.(*models.ShipBasicInfo), nil
}

// getShipTypeInfo 获取船舶类型信息
func (s *QueryService) getShipTypeInfo(ctx context.Context, session neo4j.SessionWithContext, shipName string) (*models.ShipTypeInfo, error) {
	query := `
		MATCH (s:Ship {name: $shipName})-[:IS_TYPE]->(st:ShipType)-[:BELONGS_TO_CATEGORY]->(sc:ShipCategory)
		RETURN sc.name as category, st.subName as subType, st.subCode as subCode
	`

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		params := map[string]interface{}{"shipName": shipName}
		log.Printf("[getShipTypeInfo] Executing Cypher query:\n%s\nParams: %+v", query, params)

		records, err := tx.Run(ctx, query, params)
		if err != nil {
			return nil, err
		}

		if !records.Next(ctx) {
			// 如果没有类型信息，返回默认值
			return &models.ShipTypeInfo{
				Category: "未知",
				SubType:  "未知",
				SubCode:  "",
			}, nil
		}

		record := records.Record()
		return &models.ShipTypeInfo{
			Category: utils.GetStringValue(record.Values[0]),
			SubType:  utils.GetStringValue(record.Values[1]),
			SubCode:  utils.GetStringValue(record.Values[2]),
		}, nil
	})

	if err != nil {
		return nil, err
	}

	return result.(*models.ShipTypeInfo), nil
}

// getShipRecentStats 获取船舶最近统计数据
func (s *QueryService) getShipRecentStats(ctx context.Context, session neo4j.SessionWithContext, shipName string) (*models.ShipRecentStats, error) {
	query := `
		MATCH (s:Ship {name: $shipName})<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth)
		RETURN ym.ym as period, sms.opRatio as opRatio, sms.voyages as voyages,
		       sms.loadRatio as loadRatio, sms.turnover_tonkm as turnoverTonKm,
		       sms.capacity_ton as capacityTon
		ORDER BY ym.ym DESC
		LIMIT 1
	`

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, map[string]interface{}{"shipName": shipName})
		if err != nil {
			return nil, err
		}

		if !records.Next(ctx) {
			// 如果没有统计数据，返回默认值
			return &models.ShipRecentStats{
				LatestMonth: "无数据",
			}, nil
		}

		record := records.Record()
		return &models.ShipRecentStats{
			LatestMonth:   utils.GetStringValue(record.Values[0]),
			OpRatio:       utils.GetFloatValue(record.Values[1]),
			Voyages:       utils.GetIntValue(record.Values[2]),
			LoadRatio:     utils.GetFloatValue(record.Values[3]),
			TurnoverTonKm: utils.GetFloatValue(record.Values[4]),
			CapacityTon:   utils.GetFloatValue(record.Values[5]),
		}, nil
	})

	if err != nil {
		return nil, err
	}

	return result.(*models.ShipRecentStats), nil
}

// getShipHistoricalPerformance 获取船舶历史表现
func (s *QueryService) getShipHistoricalPerformance(ctx context.Context, session neo4j.SessionWithContext, shipName string, months int) ([]models.HistoricalPerformance, error) {
	// 获取基础统计数据
	query := `
		MATCH (s:Ship {name: $shipName})<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth)
		RETURN ym.ym as period,
		       sms.opRatio as opRatio,
		       sms.voyages as voyages,
		       sms.capacity_ton as capacityTon,
		       sms.loadRatio as loadRatio,
		       sms.turnover_tonkm as turnoverTonkm,
		       sms.opTime_min as opTimeMin,
		       sms.anchorTime_min as anchorTimeMin
		ORDER BY ym.ym DESC
		LIMIT $months
	`

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, map[string]interface{}{
			"shipName": shipName,
			"months":   months,
		})
		if err != nil {
			return nil, err
		}

		var performance []models.HistoricalPerformance
		for records.Next(ctx) {
			record := records.Record()
			period := utils.GetStringValue(record.Values[0])

			// 获取该月的货物构成
			cargoComposition, _ := s.getShipMonthlyCargoComposition(ctx, tx, shipName, period)

			// 获取该月的航线分析
			routeBreakdown, _ := s.getShipMonthlyRouteBreakdown(ctx, tx, shipName, period)

			performance = append(performance, models.HistoricalPerformance{
				Period:           period,
				OpRatio:          utils.GetFloatValue(record.Values[1]),
				Voyages:          utils.GetIntValue(record.Values[2]),
				CapacityTon:      utils.GetFloatValue(record.Values[3]),
				LoadRatio:        utils.GetFloatValue(record.Values[4]),
				TurnoverTonkm:    utils.GetFloatValue(record.Values[5]),
				OpTimeMin:        utils.GetIntValue(record.Values[6]),
				AnchorTimeMin:    utils.GetIntValue(record.Values[7]),
				CargoComposition: cargoComposition,
				RouteBreakdown:   routeBreakdown,
			})
		}

		// 按时间正序排列
		sort.Slice(performance, func(i, j int) bool {
			return performance[i].Period < performance[j].Period
		})

		return performance, records.Err()
	})

	if err != nil {
		return nil, err
	}

	return result.([]models.HistoricalPerformance), nil
}

// getShipMonthlyCargoComposition 获取船舶特定月份的货物构成
func (s *QueryService) getShipMonthlyCargoComposition(ctx context.Context, tx neo4j.ManagedTransaction, shipName string, period string) ([]models.CargoComposition, error) {
	query := `
		MATCH (s:Ship {name: $shipName})<-[:CARGO_STAT_FOR_SHIP]-(smcs:ShipMonthCargoStat)-[:CARGO_STAT_FOR_TYPE]->(ct:CargoType)
		MATCH (smcs)-[:CARGO_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
		RETURN ct.subName as cargoType, smcs.cargo_ton as cargoTon
		ORDER BY smcs.cargo_ton DESC
		limit 3
	`

	records, err := tx.Run(ctx, query, map[string]interface{}{
		"shipName": shipName,
		"period":   period,
	})
	if err != nil {
		return nil, err
	}

	var composition []models.CargoComposition
	var totalTon float64

	// 先收集所有数据并计算总量
	var tempData []struct {
		cargoType string
		cargoTon  float64
	}

	for records.Next(ctx) {
		record := records.Record()
		cargoType := utils.GetStringValue(record.Values[0])
		cargoTon := utils.GetFloatValue(record.Values[1])
		tempData = append(tempData, struct {
			cargoType string
			cargoTon  float64
		}{cargoType, cargoTon})
		totalTon += cargoTon
	}

	// 计算百分比并构建最终结果
	for _, data := range tempData {
		percentage := 0.0
		if totalTon > 0 {
			percentage = data.cargoTon / totalTon
		}
		composition = append(composition, models.CargoComposition{
			CargoType:  data.cargoType,
			TotalTon:   data.cargoTon,
			Percentage: percentage,
		})
	}

	return composition, records.Err()
}

// getShipMonthlyRouteBreakdown 获取船舶特定月份的航线分析
func (s *QueryService) getShipMonthlyRouteBreakdown(ctx context.Context, tx neo4j.ManagedTransaction, shipName string, period string) ([]models.RouteBreakdown, error) {
	// 首先尝试从ShipMonthLineStat获取详细的航线数据
	query := `
		MATCH (s:Ship {name: $shipName})<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)-[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
		MATCH (smls)-[:LINE_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
		RETURN sr.routeName as route,
		       smls.voyageCount as voyages,
		       smls.cargo_ton as cargoTon,
		       smls.avgLoadRatio as avgLoadRatio
		ORDER BY smls.cargo_ton DESC
	`

	records, err := tx.Run(ctx, query, map[string]interface{}{
		"shipName": shipName,
		"period":   period,
	})
	if err != nil {
		return nil, err
	}

	var breakdown []models.RouteBreakdown
	for records.Next(ctx) {
		record := records.Record()
		breakdown = append(breakdown, models.RouteBreakdown{
			Route:        utils.GetStringValue(record.Values[0]),
			Voyages:      utils.GetIntValue(record.Values[1]),
			CargoTon:     utils.GetFloatValue(record.Values[2]),
			AvgLoadRatio: utils.GetFloatValue(record.Values[3]),
		})
	}

	// // 如果没有找到ShipMonthLineStat数据，尝试从其他数据源推导
	// if len(breakdown) == 0 {
	// 	breakdown, _ = s.getShipRouteBreakdownFromAlternativeSource(ctx, tx, shipName, period)
	// }

	return breakdown, records.Err()
}


// getPortBasicInfo 获取港口基本信息
func (s *QueryService) getPortBasicInfo(ctx context.Context, session neo4j.SessionWithContext, portName string) (map[string]interface{}, error) {
	query := `
		MATCH (p:Port {name: $portName})
		OPTIONAL MATCH (p)-[:BELONGS_TO_PROVINCE]->(prov:Province)
		OPTIONAL MATCH (p)-[:BELONGS_TO_BASIN]->(basin:Basin)
		RETURN p.name as name, p.code as code, prov.name as province, basin.name as basin
	`

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		params := map[string]interface{}{
			"portName": portName,
		}
		log.Printf("[getPortBasicInfo] Executing Cypher query:\n%s\nParams: %+v", query, params)

		records, err := tx.Run(ctx, query, params)
		if err != nil {
			return nil, err
		}

		if !records.Next(ctx) {
			return nil, fmt.Errorf("port not found: %s", portName)
		}

		record := records.Record()
		basicInfo := map[string]interface{}{
			"name":     utils.GetStringValue(record.Values[0]),
			"code":     utils.GetStringValue(record.Values[1]),
			"province": utils.GetStringValue(record.Values[2]),
			"basin":    utils.GetStringValue(record.Values[3]),
		}

		// 记录数据完整性状况
		missingFields := []string{}
		if basicInfo["code"] == "" {
			missingFields = append(missingFields, "港口代码")
		}
		if basicInfo["province"] == "" {
			missingFields = append(missingFields, "省份信息")
		}
		if basicInfo["basin"] == "" {
			missingFields = append(missingFields, "流域信息")
		}

		if len(missingFields) > 0 {
			log.Printf("[getPortBasicInfo] 港口 %s 缺少基本信息: %v", portName, missingFields)
		}

		return basicInfo, nil
	})

	if err != nil {
		return nil, err
	}

	return result.(map[string]interface{}), nil
}

// diagnosePortCargoData 诊断港口货物数据完整性
func (s *QueryService) diagnosePortCargoData(ctx context.Context, session neo4j.SessionWithContext, portName string) {
	// 检查是否有PortMonthCargoStat节点
	cargoStatQuery := `
		MATCH (p:Port {name: $portName})<-[:PORT_CARGO_STAT_FOR_PORT]-(pmcs:PortMonthCargoStat)
		RETURN count(pmcs) as cargoStatCount
	`

	session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		params := map[string]interface{}{"portName": portName}
		log.Printf("[diagnosePortCargoData] 检查货物统计节点 - Cypher:\n%s\nParams: %+v", cargoStatQuery, params)

		records, err := tx.Run(ctx, cargoStatQuery, params)
		if err != nil {
			log.Printf("[diagnosePortCargoData] 查询货物统计节点失败: %v", err)
			return nil, err
		}

		if records.Next(ctx) {
			count := utils.GetIntValue(records.Record().Values[0])
			log.Printf("[diagnosePortCargoData] 港口 %s 的货物统计节点数量: %d", portName, count)
		}
		return nil, nil
	})

	// 检查是否有货物类型关系
	cargoTypeQuery := `
		MATCH (p:Port {name: $portName})<-[:PORT_CARGO_STAT_FOR_PORT]-(pmcs:PortMonthCargoStat)-[:PORT_CARGO_STAT_FOR_TYPE]->(ct:CargoType)
		RETURN count(DISTINCT ct) as cargoTypeCount
	`

	session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		params := map[string]interface{}{"portName": portName}
		log.Printf("[diagnosePortCargoData] 检查货物类型关系 - Cypher:\n%s\nParams: %+v", cargoTypeQuery, params)

		records, err := tx.Run(ctx, cargoTypeQuery, params)
		if err != nil {
			log.Printf("[diagnosePortCargoData] 查询货物类型关系失败: %v", err)
			return nil, err
		}

		if records.Next(ctx) {
			count := utils.GetIntValue(records.Record().Values[0])
			log.Printf("[diagnosePortCargoData] 港口 %s 关联的货物类型数量: %d", portName, count)
		}
		return nil, nil
	})
}

// getPortRecentStats 获取港口最新统计数据
func (s *QueryService) getPortRecentStats(ctx context.Context, session neo4j.SessionWithContext, portName string, period string) (map[string]interface{}, error) {
	query := `
		MATCH (p:Port {name: $portName})<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
		RETURN pms.inShipCount as inShipCount, pms.outShipCount as outShipCount,
		       pms.inCargo_ton as inCargoTon, pms.outCargo_ton as outCargoTon,
		       pms.inLoadRatio as inLoadRatio, pms.outLoadRatio as outLoadRatio,
		       pms.anchorTime_days as anchorTimeDays
	`

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		params := map[string]interface{}{
			"portName": portName,
			"period":   period,
		}
		log.Printf("[getPortRecentStats] Executing Cypher query:\n%s\nParams: %+v", query, params)

		records, err := tx.Run(ctx, query, params)
		if err != nil {
			return nil, err
		}

		if !records.Next(ctx) {
			return map[string]interface{}{
				"latest_month": period,
				"message":      "无数据",
			}, nil
		}

		record := records.Record()
		inCargoTon := utils.GetFloatValue(record.Values[2])
		outCargoTon := utils.GetFloatValue(record.Values[3])

		return map[string]interface{}{
			"latest_month":        period,
			"inShipCount":         utils.GetIntValue(record.Values[0]),
			"outShipCount":        utils.GetIntValue(record.Values[1]),
			"inCargo_ton":         inCargoTon,
			"outCargo_ton":        outCargoTon,
			"totalThroughput_ton": inCargoTon + outCargoTon,
			"inLoadRatio":         utils.GetFloatValue(record.Values[4]),
			"outLoadRatio":        utils.GetFloatValue(record.Values[5]),
			"avgAnchorTime_days":  utils.GetFloatValue(record.Values[6]),
		}, nil
	})

	if err != nil {
		return nil, err
	}

	return result.(map[string]interface{}), nil
}

// getPortHistoricalPerformance 获取港口历史表现
func (s *QueryService) getPortHistoricalPerformance(ctx context.Context, session neo4j.SessionWithContext, portName string, months int) ([]map[string]interface{}, error) {
	query := `
		MATCH (p:Port {name: $portName})<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth)
		RETURN ym.ym as period,
		       pms.inShipCount as inShipCount,
		       pms.outShipCount as outShipCount,
		       pms.inCargo_ton as inCargoTon,
		       pms.outCargo_ton as outCargoTon,
		       pms.inLoadRatio as inLoadRatio,
		       pms.outLoadRatio as outLoadRatio,
		       pms.anchorTime_days as anchorTimeDays
		ORDER BY ym.ym DESC
		LIMIT $months
	`

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		params := map[string]interface{}{
			"portName": portName,
			"months":   months,
		}
		log.Printf("[getPortHistoricalPerformance] Executing Cypher query:\n%s\nParams: %+v", query, params)

		records, err := tx.Run(ctx, query, params)
		if err != nil {
			return nil, err
		}

		var performance []map[string]interface{}
		for records.Next(ctx) {
			record := records.Record()
			period := utils.GetStringValue(record.Values[0])
			inCargoTon := utils.GetFloatValue(record.Values[3])
			outCargoTon := utils.GetFloatValue(record.Values[4])

			// 获取该月的货物构成
			cargoBreakdown, _ := s.getPortMonthlyCargoBreakdown(ctx, tx, portName, period)

			// 获取该月的船舶类型构成
			shipTypeBreakdown, _ := s.getPortMonthlyShipTypeBreakdown(ctx, tx, portName, period)

			performance = append(performance, map[string]interface{}{
				"period":              period,
				"inShipCount":         utils.GetIntValue(record.Values[1]),
				"outShipCount":        utils.GetIntValue(record.Values[2]),
				"inCargo_ton":         inCargoTon,
				"outCargo_ton":        outCargoTon,
				"totalThroughput_ton": inCargoTon + outCargoTon,
				"inLoadRatio":         utils.GetFloatValue(record.Values[5]),
				"outLoadRatio":        utils.GetFloatValue(record.Values[6]),
				"avgAnchorTime_hours": utils.GetFloatValue(record.Values[7]) * 24, // 转换为小时
				"cargo_breakdown":     cargoBreakdown,
				"ship_type_breakdown": shipTypeBreakdown,
			})
		}

		// 按时间正序排列
		sort.Slice(performance, func(i, j int) bool {
			return performance[i]["period"].(string) < performance[j]["period"].(string)
		})

		return performance, records.Err()
	})

	if err != nil {
		return nil, err
	}

	return result.([]map[string]interface{}), nil
}

// calculateAvgThroughput 计算平均吞吐量
func (s *QueryService) calculateAvgThroughput(performance []map[string]interface{}) float64 {
	if len(performance) == 0 {
		return 0
	}

	var total float64
	for _, p := range performance {
		if throughput, ok := p["totalThroughput_ton"].(float64); ok {
			total += throughput
		}
	}

	return total / float64(len(performance))
}

// calculatePortPerformanceRating 计算港口性能评级
func (s *QueryService) calculatePortPerformanceRating(performance []map[string]interface{}) string {
	if len(performance) == 0 {
		return "无数据"
	}

	// 简单的评级逻辑，基于平均吞吐量
	avgThroughput := s.calculateAvgThroughput(performance)

	if avgThroughput > 15000000 { // 1500万吨以上
		return "优秀"
	} else if avgThroughput > 10000000 { // 1000万吨以上
		return "良好"
	} else if avgThroughput > 5000000 { // 500万吨以上
		return "一般"
	} else {
		return "待提升"
	}
}

// getPortMonthlyCargoBreakdown 获取港口特定月份的货物构成
func (s *QueryService) getPortMonthlyCargoBreakdown(ctx context.Context, tx neo4j.ManagedTransaction, portName string, period string) ([]map[string]interface{}, error) {
	query := `
		MATCH (p:Port {name: $portName})<-[:PORT_CARGO_STAT_FOR_PORT]-(pmcs:PortMonthCargoStat)-[:PORT_CARGO_STAT_FOR_TYPE]->(ct:CargoType)
		MATCH (pmcs)-[:PORT_CARGO_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
		RETURN ct.subName as cargoType,
		       pmcs.inCargo_ton as inCargoTon,
		       pmcs.outCargo_ton as outCargoTon
		ORDER BY (pmcs.inCargo_ton + pmcs.outCargo_ton) DESC
		limit 3
	`

	// 打印Cypher查询日志
	params := map[string]interface{}{
		"portName": portName,
		"period":   period,
	}
	log.Printf("[getPortMonthlyCargoBreakdown] Executing Cypher query:\n%s\nParams: %+v", query, params)

	records, err := tx.Run(ctx, query, params)
	if err != nil {
		log.Printf("[getPortMonthlyCargoBreakdown] Query execution failed: %v", err)
		return nil, err
	}

	// 检查是否有数据
	hasData := false
	for records.Next(ctx) {
		hasData = true
		break
	}

	if !hasData {
		log.Printf("[getPortMonthlyCargoBreakdown] 港口 %s 在 %s 期间无货物统计数据", portName, period)
		// 重新执行查询以获取实际数据
		records, err = tx.Run(ctx, query, map[string]interface{}{
			"portName": portName,
			"period":   period,
		})
		if err != nil {
			return nil, err
		}
	}

	var breakdown []map[string]interface{}
	var totalTon float64

	// 先收集所有数据并计算总量
	var tempData []struct {
		cargoType   string
		inCargoTon  float64
		outCargoTon float64
		totalTon    float64
	}

	for records.Next(ctx) {
		record := records.Record()
		cargoType := utils.GetStringValue(record.Values[0])
		inCargoTon := utils.GetFloatValue(record.Values[1])
		outCargoTon := utils.GetFloatValue(record.Values[2])
		total := inCargoTon + outCargoTon

		tempData = append(tempData, struct {
			cargoType   string
			inCargoTon  float64
			outCargoTon float64
			totalTon    float64
		}{cargoType, inCargoTon, outCargoTon, total})
		totalTon += total
	}

	// 计算百分比并构建最终结果
	for _, data := range tempData {
		percentage := 0.0
		if totalTon > 0 {
			percentage = data.totalTon / totalTon
		}
		breakdown = append(breakdown, map[string]interface{}{
			"cargo_type":   data.cargoType,
			"inCargo_ton":  data.inCargoTon,
			"outCargo_ton": data.outCargoTon,
			"percentage":   percentage,
		})
	}

	return breakdown, records.Err()
}

// getPortMonthlyShipTypeBreakdown 获取港口特定月份的船舶类型构成
func (s *QueryService) getPortMonthlyShipTypeBreakdown(ctx context.Context, tx neo4j.ManagedTransaction, portName string, period string) ([]map[string]interface{}, error) {
	query := `
		// 通过航线统计数据获取在该港口运营的船舶类型分解
		MATCH (p:Port {name: $portName})
		MATCH (sr:ShippingRoute)
		WHERE (sr)-[:ROUTE_ORIGIN]->(p) OR (sr)-[:ROUTE_DESTINATION]->(p)

		// 获取在该航线运营的船舶月度统计
		MATCH (sr)<-[:STAT_FOR_ROUTE]-(smls:ShipMonthLineStat)-[:LINE_STAT_FOR_SHIP]->(s:Ship)
		MATCH (smls)-[:LINE_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
		MATCH (s)-[:IS_TYPE]->(st:ShipType)

		// 聚合船舶类型数据
		WITH st.subName as shipType,
		     COUNT(DISTINCT s) as shipCount,
		     SUM(smls.cargo_ton) as cargoTon

		WHERE cargoTon > 0
		RETURN shipType, shipCount, cargoTon
		ORDER BY cargoTon DESC
		LIMIT 5
	`

	records, err := tx.Run(ctx, query, map[string]interface{}{
		"portName": portName,
		"period":   period,
	})
	if err != nil {
		return nil, err
	}

	var breakdown []map[string]interface{}
	var totalCargoTon float64

	// 先收集所有数据并计算总量
	var tempData []struct {
		shipType  string
		shipCount int
		cargoTon  float64
	}

	for records.Next(ctx) {
		record := records.Record()
		shipType := utils.GetStringValue(record.Values[0])
		shipCount := utils.GetIntValue(record.Values[1])
		cargoTon := utils.GetFloatValue(record.Values[2])

		tempData = append(tempData, struct {
			shipType  string
			shipCount int
			cargoTon  float64
		}{shipType, shipCount, cargoTon})
		totalCargoTon += cargoTon
	}

	// 计算百分比并构建最终结果
	for _, data := range tempData {
		percentage := 0.0
		if totalCargoTon > 0 {
			percentage = data.cargoTon / totalCargoTon
		}
		breakdown = append(breakdown, map[string]interface{}{
			"ship_type":  data.shipType,
			"ship_count": data.shipCount,
			"cargo_ton":  data.cargoTon,
			"percentage": percentage,
		})
	}

	return breakdown, records.Err()
}

// getShipCargoComposition 获取船舶货物构成
func (s *QueryService) getShipCargoComposition(ctx context.Context, session neo4j.SessionWithContext, shipName string, months int) ([]models.CargoComposition, error) {
	query := `
		MATCH (s:Ship {name: $shipName})<-[:CARGO_STAT_FOR_SHIP]-(smcs:ShipMonthCargoStat)-[:CARGO_STAT_FOR_TYPE]->(ct:CargoType)
		MATCH (smcs)-[:CARGO_STAT_FOR_MONTH]->(ym:YearMonth)
		WHERE ym.ym >= $startPeriod
		RETURN ct.subName as cargoType, SUM(smcs.cargo_ton) as totalTon, COUNT(DISTINCT ym.ym) as monthsActive
		ORDER BY totalTon DESC
	`

	// 计算起始时间（最近N个月）
	currentTime := time.Now()
	startTime := currentTime.AddDate(0, -months, 0)
	startPeriod := startTime.Format("200601")

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, map[string]interface{}{
			"shipName":    shipName,
			"startPeriod": startPeriod,
		})
		if err != nil {
			return nil, err
		}

		var composition []models.CargoComposition
		var totalCargo float64

		for records.Next(ctx) {
			record := records.Record()
			cargoTon := utils.GetFloatValue(record.Values[1])
			composition = append(composition, models.CargoComposition{
				CargoType:    utils.GetStringValue(record.Values[0]),
				TotalTon:     cargoTon,
				MonthsActive: utils.GetIntValue(record.Values[2]),
			})
			totalCargo += cargoTon
		}

		// 计算百分比
		for i := range composition {
			if totalCargo > 0 {
				composition[i].Percentage = composition[i].TotalTon / totalCargo * 100
			}
		}

		return composition, records.Err()
	})

	if err != nil {
		return nil, err
	}

	return result.([]models.CargoComposition), nil
}

// getShipRouteAnalysis 获取船舶航线分析
func (s *QueryService) getShipRouteAnalysis(ctx context.Context, session neo4j.SessionWithContext, shipName string, months int) ([]models.RouteAnalysis, error) {
	query := `
		MATCH (s:Ship {name: $shipName})<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)-[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
		MATCH (smls)-[:LINE_STAT_FOR_MONTH]->(ym:YearMonth)
		WHERE ym.ym >= $startPeriod
		RETURN sr.routeName as route, COUNT(*) as frequency, 
		       SUM(smls.cargo_ton) as totalCargo, AVG(smls.cargo_ton) as avgCargoPerTrip
		ORDER BY totalCargo DESC
	`

	// 计算起始时间
	currentTime := time.Now()
	startTime := currentTime.AddDate(0, -months, 0)
	startPeriod := startTime.Format("200601")

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, map[string]interface{}{
			"shipName":    shipName,
			"startPeriod": startPeriod,
		})
		if err != nil {
			return nil, err
		}

		var analysis []models.RouteAnalysis
		for records.Next(ctx) {
			record := records.Record()
			analysis = append(analysis, models.RouteAnalysis{
				Route:           utils.GetStringValue(record.Values[0]),
				Frequency:       utils.GetIntValue(record.Values[1]),
				TotalCargo:      utils.GetFloatValue(record.Values[2]),
				AvgCargoPerTrip: utils.GetFloatValue(record.Values[3]),
			})
		}

		return analysis, records.Err()
	})

	if err != nil {
		return nil, err
	}

	return result.([]models.RouteAnalysis), nil
}

// getShipRelationships 获取船舶关系信息
func (s *QueryService) getShipRelationships(ctx context.Context, session neo4j.SessionWithContext, shipName string) (*models.ShipRelationships, error) {
	// 获取船舶的注册港口和省份
	query := `
		MATCH (s:Ship {name: $shipName})
		RETURN s.regPortName as homePort, s.regPortProvince as operatingProvince
	`

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, map[string]interface{}{"shipName": shipName})
		if err != nil {
			return nil, err
		}

		relationships := &models.ShipRelationships{
			Basin: "长江", // 默认为长江
		}

		if records.Next(ctx) {
			record := records.Record()
			relationships.HomePort = utils.GetStringValue(record.Values[0])
			relationships.OperatingProvince = utils.GetStringValue(record.Values[1])
		}

		// 获取常去港口（基于航线统计）
		frequentPortsQuery := `
			MATCH (s:Ship {name: $shipName})<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)-[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
			MATCH (sr)-[:ROUTE_ORIGIN|ROUTE_DESTINATION]->(p:Port)
			RETURN DISTINCT p.name as portName
			LIMIT 5
		`

		portRecords, err := tx.Run(ctx, frequentPortsQuery, map[string]interface{}{"shipName": shipName})
		if err == nil {
			var frequentPorts []string
			for portRecords.Next(ctx) {
				portRecord := portRecords.Record()
				portName := utils.GetStringValue(portRecord.Values[0])
				if portName != relationships.HomePort {
					frequentPorts = append(frequentPorts, portName)
				}
			}
			relationships.FrequentPorts = frequentPorts
		}

		return relationships, nil
	})

	if err != nil {
		return nil, err
	}

	return result.(*models.ShipRelationships), nil
}

// getShipRealtimeStatus 获取船舶实时状态
func (s *QueryService) getShipRealtimeStatus(ctx context.Context, session neo4j.SessionWithContext, shipName string) (*models.ShipRealtime, error) {
	query := `
		MATCH (s:Ship {name: $shipName})-[:HAS_REALTIME]->(sr:ShipRealtime)
		RETURN sr.lat as lat, sr.lon as lon, sr.sog as sog, sr.cog as cog,
		       sr.navStatus as navStatus, sr.aisTimestamp as aisTimestamp,
		       sr.portStatus as portStatus, sr.portReport as portReport,
		       sr.lastUpdated as lastUpdated
	`

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		records, err := tx.Run(ctx, query, map[string]interface{}{"shipName": shipName})
		if err != nil {
			return nil, err
		}

		if !records.Next(ctx) {
			return nil, nil // 没有实时数据
		}

		record := records.Record()
		realtimeStatus := &models.ShipRealtime{
			Lat:        utils.GetFloatValue(record.Values[0]),
			Lon:        utils.GetFloatValue(record.Values[1]),
			SOG:        utils.GetFloatValue(record.Values[2]),
			COG:        utils.GetFloatValue(record.Values[3]),
			NavStatus:  utils.GetStringValue(record.Values[4]),
			PortStatus: utils.GetStringValue(record.Values[6]),
			PortReport: utils.GetStringValue(record.Values[7]),
		}

		// 处理时间字段
		if record.Values[5] != nil {
			if timeVal, ok := record.Values[5].(time.Time); ok {
				realtimeStatus.AISTimestamp = timeVal
			}
		}
		if record.Values[8] != nil {
			if timeVal, ok := record.Values[8].(time.Time); ok {
				realtimeStatus.LastUpdated = timeVal
			}
		}

		return realtimeStatus, nil
	})

	if err != nil {
		return nil, err
	}

	if result == nil {
		return nil, nil
	}

	return result.(*models.ShipRealtime), nil
}

// generateShipProfileSummary 生成船舶画像摘要
func (s *QueryService) generateShipProfileSummary(
	historical []models.HistoricalPerformance,
	cargo []models.CargoComposition,
	routes []models.RouteAnalysis,
) *models.ShipProfileSummary {
	summary := &models.ShipProfileSummary{
		TotalMonthsData: len(historical),
	}

	// 计算平均月度货运量
	if len(historical) > 0 {
		var totalCapacity float64
		for _, h := range historical {
			totalCapacity += h.CapacityTon
		}
		summary.AvgMonthlyCapacity = totalCapacity / float64(len(historical))
	}

	// 提取主要货物类型（前3个）
	if len(cargo) > 0 {
		maxTypes := 3
		if len(cargo) < maxTypes {
			maxTypes = len(cargo)
		}
		for i := 0; i < maxTypes; i++ {
			summary.PrimaryCargoTypes = append(summary.PrimaryCargoTypes, cargo[i].CargoType)
		}
	}

	// 提取主要航线（前3个）
	if len(routes) > 0 {
		maxRoutes := 3
		if len(routes) < maxRoutes {
			maxRoutes = len(routes)
		}
		for i := 0; i < maxRoutes; i++ {
			summary.MainRoutes = append(summary.MainRoutes, routes[i].Route)
		}
	}

	// 简单的性能评级
	if summary.AvgMonthlyCapacity > 10000 {
		summary.PerformanceRating = "优秀"
	} else if summary.AvgMonthlyCapacity > 5000 {
		summary.PerformanceRating = "良好"
	} else if summary.AvgMonthlyCapacity > 1000 {
		summary.PerformanceRating = "一般"
	} else {
		summary.PerformanceRating = "待改善"
	}

	return summary
}

// executePortProfileQuery 执行港口画像查询
func (s *QueryService) executePortProfileQuery(ctx context.Context, entity *models.EntityInfo, timeQuery *timeparser.TimeQuery, options interface{}) (interface{}, error) {
	session := s.driver.NewSession(ctx, neo4j.SessionConfig{DatabaseName: "neo4j"})
	defer session.Close(ctx)

	if len(timeQuery.MonthNodes) == 0 {
		return nil, fmt.Errorf("no time periods specified for port profile")
	}

	// 诊断港口货物数据完整性
	s.diagnosePortCargoData(ctx, session, entity.Name)

	// 获取港口基本信息
	basicInfo, err := s.getPortBasicInfo(ctx, session, entity.Name)
	if err != nil {
		return nil, fmt.Errorf("failed to get port basic info: %v", err)
	}

	// 获取最新统计数据
	latestPeriod := timeQuery.MonthNodes[len(timeQuery.MonthNodes)-1]
	recentStats, err := s.getPortRecentStats(ctx, session, entity.Name, latestPeriod)
	if err != nil {
		return nil, fmt.Errorf("failed to get port recent stats: %v", err)
	}

	// 获取历史表现数据（包含完整的货物和船舶分解数据）
	historicalPerformance, err := s.getPortHistoricalPerformanceWithDetails(ctx, session, entity.Name, timeQuery.MonthNodes)
	if err != nil {
		return nil, fmt.Errorf("failed to get port historical performance: %v", err)
	}


	// 构建完整的港口画像响应
	profile := map[string]interface{}{
		"basic_info":             basicInfo,
		"recent_stats":           recentStats,
		"historical_performance": historicalPerformance,
		// "cargo_breakdown":        cargoBreakdown,
		// "route_connections":      routeConnections,
		"summary": map[string]interface{}{
			"total_months_data":      len(historicalPerformance),
			"avg_monthly_throughput": s.calculateAvgThroughput(historicalPerformance),
			"performance_rating":     s.calculatePortPerformanceRating(historicalPerformance),
		},
	}

	return profile, nil
}


// getPortRouteConnections 获取港口的航线连接信息
func (s *QueryService) getPortRouteConnections(ctx context.Context, session neo4j.SessionWithContext, portName string, period string) ([]map[string]interface{}, error) {
	query := `
		MATCH (p:Port {name: $portName})
		MATCH (sr:ShippingRoute)
		WHERE (sr)-[:ROUTE_ORIGIN]->(p) OR (sr)-[:ROUTE_DESTINATION]->(p)

		// 获取航线的另一端港口
		OPTIONAL MATCH (sr)-[:ROUTE_ORIGIN]->(originPort:Port)
		OPTIONAL MATCH (sr)-[:ROUTE_DESTINATION]->(destPort:Port)
		WITH sr, p,
		     CASE WHEN originPort.name = $portName THEN destPort ELSE originPort END as otherPort

		// 获取该航线在指定时间的统计数据
		OPTIONAL MATCH (sr)<-[:ROUTE_STAT_FOR_ROUTE]-(rms:RouteMonthStat)-[:ROUTE_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})

		WHERE otherPort IS NOT NULL AND (rms.totalShipCount > 0 OR rms.totalCargo_ton > 0)
		RETURN otherPort.name as destination,
		       sr.routeName as route_name,
		       COALESCE(rms.totalShipCount, 0) as ship_count,
		       COALESCE(rms.totalCargo_ton, 0) as cargo_volume,
		       COALESCE(rms.totalVoyageCount, 0) as voyage_count
		ORDER BY cargo_volume DESC
		LIMIT 3
	`

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		params := map[string]interface{}{
			"portName": portName,
			"period":   period,
		}
		log.Printf("[getPortRouteConnections] Executing Cypher query:\n%s\nParams: %+v", query, params)

		records, err := tx.Run(ctx, query, params)
		if err != nil {
			log.Printf("[getPortRouteConnections] Query execution failed: %v", err)
			return nil, err
		}

		var connections []map[string]interface{}
		for records.Next(ctx) {
			record := records.Record()
			destination := utils.GetStringValue(record.Values[0])
			routeName := utils.GetStringValue(record.Values[1])
			shipCount := utils.GetIntValue(record.Values[2])
			cargoVolume := utils.GetFloatValue(record.Values[3])
			voyageCount := utils.GetIntValue(record.Values[4])

			if destination != "" && (shipCount > 0 || cargoVolume > 0) {
				connections = append(connections, map[string]interface{}{
					"destination":  destination,
					"route_name":   routeName,
					"ship_count":   shipCount,
					"cargo_volume": cargoVolume,
					"voyage_count": voyageCount,
				})
			}
		}

		log.Printf("[getPortRouteConnections] Found %d route connections for port: %s", len(connections), portName)
		return connections, records.Err()
	})

	if err != nil {
		return nil, err
	}

	return result.([]map[string]interface{}), nil
}

// getPortHistoricalPerformanceWithDetails 获取港口指定时间段的完整历史表现（包含货物和船舶分解）
func (s *QueryService) getPortHistoricalPerformanceWithDetails(ctx context.Context, session neo4j.SessionWithContext, portName string, periods []string) ([]map[string]interface{}, error) {
	query := `
		MATCH (p:Port {name: $portName})<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth)
		WHERE ym.ym IN $periods
		RETURN ym.ym as period,
		       pms.inShipCount as inShipCount,
		       pms.outShipCount as outShipCount,
		       pms.inCargo_ton as inCargoTon,
		       pms.outCargo_ton as outCargoTon,
		       pms.inLoadRatio as inLoadRatio,
		       pms.outLoadRatio as outLoadRatio,
		       pms.anchorTime_days as anchorTimeDays
		ORDER BY ym.ym DESC
	`

	result, err := session.ExecuteRead(ctx, func(tx neo4j.ManagedTransaction) (interface{}, error) {
		params := map[string]interface{}{
			"portName": portName,
			"periods":  periods,
		}
		log.Printf("[getPortHistoricalPerformanceWithDetails] Executing Cypher query:\n%s\nParams: %+v", query, params)

		records, err := tx.Run(ctx, query, params)
		if err != nil {
			return nil, err
		}

		var periodsData []map[string]interface{} // 临时存储基础数据

		for records.Next(ctx) {
			record := records.Record()
			period := utils.GetStringValue(record.Values[0])
			inCargoTon := utils.GetFloatValue(record.Values[3])
			outCargoTon := utils.GetFloatValue(record.Values[4])

			// 获取该月的货物构成
			cargoBreakdown, _ := s.getPortMonthlyCargoBreakdown(ctx, tx, portName, period)

			// 获取该月的船舶类型构成
			shipTypeBreakdown, _ := s.getPortMonthlyShipTypeBreakdown(ctx, tx, portName, period)

			routeConnections, _ := s.getPortRouteConnections(ctx, session, portName, period)

			// 先存储基础数据，航线连接信息需要在事务外获取
			periodsData = append(periodsData, map[string]interface{}{
				"period":              period,
				"inShipCount":         utils.GetIntValue(record.Values[1]),
				"outShipCount":        utils.GetIntValue(record.Values[2]),
				"inCargo_ton":         inCargoTon,
				"outCargo_ton":        outCargoTon,
				"totalThroughput_ton": inCargoTon + outCargoTon,
				"inLoadRatio":         utils.GetFloatValue(record.Values[5]),
				"outLoadRatio":        utils.GetFloatValue(record.Values[6]),
				"avgAnchorTime_hours": utils.GetFloatValue(record.Values[7]) * 24, // 转换为小时
				"cargo_breakdown":     cargoBreakdown,
				"route_breakdown":     routeConnections,
				"ship_type_breakdown": shipTypeBreakdown,
			})
		}

		log.Printf("[getPortHistoricalPerformanceWithDetails] Found %d periods of basic performance data for port: %s", len(periodsData), portName)
		return periodsData, nil
	})

	if err != nil {
		return nil, err
	}

	periodsData := result.([]map[string]interface{})

	log.Printf("[getPortHistoricalPerformanceWithDetails] Found %d periods of detailed performance data for port: %s", len(periodsData), portName)
	return periodsData, nil
}
