package time

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// TimeQuery 时间查询条件
type TimeQuery struct {
	YearNodes   []int    `json:"year_nodes"`    // Year节点列表
	MonthNodes  []string `json:"month_nodes"`   // YearMonth节点列表
	Granularity string   `json:"granularity"`   // 查询粒度: year|month|day
	IsDegraded  bool     `json:"is_degraded"`   // 是否降级处理
}

// CSTELParser C-STEL时间表达式解析器
type CSTELParser struct {
	currentDate time.Time
}

// NewCSTELParser 创建新的C-STEL解析器
func NewCSTELParser() *CSTELParser {
	return &CSTELParser{
		currentDate: time.Now(),
	}
}

// SetCurrentDate 设置当前日期（用于测试）
func (p *CSTELParser) SetCurrentDate(date time.Time) {
	p.currentDate = date
}

// Parse 解析C-STEL表达式为数据库查询条件
func (p *CSTELParser) Parse(expression string) (*TimeQuery, error) {
	expression = strings.TrimSpace(expression)
	if expression == "" {
		return nil, fmt.Errorf("empty time expression")
	}

	// 处理相对时间表达式
	if relativeQuery, err := p.parseRelativeTime(expression); err == nil {
		return relativeQuery, nil
	}

	// 处理绝对时间表达式
	if absoluteQuery, err := p.parseAbsoluteTime(expression); err == nil {
		return absoluteQuery, nil
	}

	// 处理时间范围表达式
	if rangeQuery, err := p.parseTimeRange(expression); err == nil {
		return rangeQuery, nil
	}

	// 处理季度表达式
	if quarterQuery, err := p.parseQuarter(expression); err == nil {
		return quarterQuery, nil
	}

	return nil, fmt.Errorf("unsupported time expression: %s", expression)
}

// parseRelativeTime 解析相对时间表达式
func (p *CSTELParser) parseRelativeTime(expression string) (*TimeQuery, error) {
	// R6M - 最近6个月
	if matched, _ := regexp.MatchString(`^R(\d+)M$`, expression); matched {
		re := regexp.MustCompile(`^R(\d+)M$`)
		matches := re.FindStringSubmatch(expression)
		months, _ := strconv.Atoi(matches[1])
		
		var monthNodes []string
		for i := months - 1; i >= 0; i-- {
			date := p.currentDate.AddDate(0, -i, 0)
			monthNodes = append(monthNodes, date.Format("200601"))
		}
		
		return &TimeQuery{
			MonthNodes:  monthNodes,
			Granularity: "month",
			IsDegraded:  false,
		}, nil
	}

	// R3Y - 最近3年
	if matched, _ := regexp.MatchString(`^R(\d+)Y$`, expression); matched {
		re := regexp.MustCompile(`^R(\d+)Y$`)
		matches := re.FindStringSubmatch(expression)
		years, _ := strconv.Atoi(matches[1])
		
		var yearNodes []int
		for i := years - 1; i >= 0; i-- {
			year := p.currentDate.Year() - i
			yearNodes = append(yearNodes, year)
		}
		
		return &TimeQuery{
			YearNodes:   yearNodes,
			Granularity: "year",
			IsDegraded:  false,
		}, nil
	}

	return nil, fmt.Errorf("not a relative time expression")
}

// parseAbsoluteTime 解析绝对时间表达式
func (p *CSTELParser) parseAbsoluteTime(expression string) (*TimeQuery, error) {
	// Y2024 - 年份
	if matched, _ := regexp.MatchString(`^Y(\d{4})$`, expression); matched {
		re := regexp.MustCompile(`^Y(\d{4})$`)
		matches := re.FindStringSubmatch(expression)
		year, _ := strconv.Atoi(matches[1])
		
		return &TimeQuery{
			YearNodes:   []int{year},
			Granularity: "year",
			IsDegraded:  false,
		}, nil
	}

	// M202406 - 年月
	if matched, _ := regexp.MatchString(`^M(\d{6})$`, expression); matched {
		re := regexp.MustCompile(`^M(\d{6})$`)
		matches := re.FindStringSubmatch(expression)
		ym := matches[1]
		
		return &TimeQuery{
			MonthNodes:  []string{ym},
			Granularity: "month",
			IsDegraded:  false,
		}, nil
	}

	// D20240630 - 日期（降级到月）
	if matched, _ := regexp.MatchString(`^D(\d{8})$`, expression); matched {
		re := regexp.MustCompile(`^D(\d{8})$`)
		matches := re.FindStringSubmatch(expression)
		dateStr := matches[1]
		
		// 提取年月
		ym := dateStr[:6]
		
		return &TimeQuery{
			MonthNodes:  []string{ym},
			Granularity: "month",
			IsDegraded:  true, // 标记为降级处理
		}, nil
	}

	return nil, fmt.Errorf("not an absolute time expression")
}

// parseTimeRange 解析时间范围表达式
func (p *CSTELParser) parseTimeRange(expression string) (*TimeQuery, error) {
	// M202401_M202406 - 月份范围
	if matched, _ := regexp.MatchString(`^M(\d{6})_M(\d{6})$`, expression); matched {
		re := regexp.MustCompile(`^M(\d{6})_M(\d{6})$`)
		matches := re.FindStringSubmatch(expression)
		startYM := matches[1]
		endYM := matches[2]
		
		monthNodes, err := p.generateMonthRange(startYM, endYM)
		if err != nil {
			return nil, err
		}
		
		return &TimeQuery{
			MonthNodes:  monthNodes,
			Granularity: "month",
			IsDegraded:  false,
		}, nil
	}

	// Y2023,Y2024 - 多个年份
	if matched, _ := regexp.MatchString(`^Y\d{4}(,Y\d{4})*$`, expression); matched {
		yearStrs := strings.Split(expression, ",")
		var yearNodes []int
		
		for _, yearStr := range yearStrs {
			re := regexp.MustCompile(`^Y(\d{4})$`)
			matches := re.FindStringSubmatch(strings.TrimSpace(yearStr))
			if len(matches) != 2 {
				return nil, fmt.Errorf("invalid year format: %s", yearStr)
			}
			year, _ := strconv.Atoi(matches[1])
			yearNodes = append(yearNodes, year)
		}
		
		return &TimeQuery{
			YearNodes:   yearNodes,
			Granularity: "year",
			IsDegraded:  false,
		}, nil
	}

	return nil, fmt.Errorf("not a time range expression")
}

// parseQuarter 解析季度表达式
func (p *CSTELParser) parseQuarter(expression string) (*TimeQuery, error) {
	// Q2024Q1 - 季度
	if matched, _ := regexp.MatchString(`^Q(\d{4})Q([1-4])$`, expression); matched {
		re := regexp.MustCompile(`^Q(\d{4})Q([1-4])$`)
		matches := re.FindStringSubmatch(expression)
		year, _ := strconv.Atoi(matches[1])
		quarter, _ := strconv.Atoi(matches[2])
		
		monthNodes := p.quarterToMonths(year, quarter)
		
		return &TimeQuery{
			MonthNodes:  monthNodes,
			Granularity: "month",
			IsDegraded:  false,
		}, nil
	}

	// AY2024_Q - 全年按季度
	if matched, _ := regexp.MatchString(`^AY(\d{4})_Q$`, expression); matched {
		re := regexp.MustCompile(`^AY(\d{4})_Q$`)
		matches := re.FindStringSubmatch(expression)
		year, _ := strconv.Atoi(matches[1])
		
		var monthNodes []string
		for quarter := 1; quarter <= 4; quarter++ {
			quarterMonths := p.quarterToMonths(year, quarter)
			monthNodes = append(monthNodes, quarterMonths...)
		}
		
		return &TimeQuery{
			MonthNodes:  monthNodes,
			Granularity: "month",
			IsDegraded:  false,
		}, nil
	}

	return nil, fmt.Errorf("not a quarter expression")
}

// generateMonthRange 生成月份范围
func (p *CSTELParser) generateMonthRange(startYM, endYM string) ([]string, error) {
	startDate, err := time.Parse("200601", startYM)
	if err != nil {
		return nil, fmt.Errorf("invalid start month: %s", startYM)
	}
	
	endDate, err := time.Parse("200601", endYM)
	if err != nil {
		return nil, fmt.Errorf("invalid end month: %s", endYM)
	}
	
	if startDate.After(endDate) {
		return nil, fmt.Errorf("start month %s is after end month %s", startYM, endYM)
	}
	
	var monthNodes []string
	current := startDate
	
	for !current.After(endDate) {
		monthNodes = append(monthNodes, current.Format("200601"))
		current = current.AddDate(0, 1, 0)
	}
	
	return monthNodes, nil
}

// quarterToMonths 将季度转换为月份列表
func (p *CSTELParser) quarterToMonths(year, quarter int) []string {
	var months []string
	startMonth := (quarter-1)*3 + 1
	
	for i := 0; i < 3; i++ {
		month := startMonth + i
		ym := fmt.Sprintf("%04d%02d", year, month)
		months = append(months, ym)
	}
	
	return months
}

// GetCurrentYearMonth 获取当前年月
func (p *CSTELParser) GetCurrentYearMonth() string {
	return p.currentDate.Format("200601")
}

// GetCurrentYear 获取当前年份
func (p *CSTELParser) GetCurrentYear() int {
	return p.currentDate.Year()
}
