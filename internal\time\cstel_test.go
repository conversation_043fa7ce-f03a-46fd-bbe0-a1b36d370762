package time

import (
	"testing"
	"time"
)

func TestCSTELParser_Parse(t *testing.T) {
	parser := NewCSTELParser()
	// 设置固定的测试时间：2025年7月30日
	testTime := time.Date(2025, 7, 30, 0, 0, 0, 0, time.UTC)
	parser.SetCurrentDate(testTime)

	tests := []struct {
		name        string
		expression  string
		wantErr     bool
		expectQuery *TimeQuery
	}{
		{
			name:       "年份表达式",
			expression: "Y2024",
			wantErr:    false,
			expectQuery: &TimeQuery{
				YearNodes:   []int{2024},
				Granularity: "year",
				IsDegraded:  false,
			},
		},
		{
			name:       "月份表达式",
			expression: "M202506",
			wantErr:    false,
			expectQuery: &TimeQuery{
				MonthNodes:  []string{"202506"},
				Granularity: "month",
				IsDegraded:  false,
			},
		},
		{
			name:       "日期表达式（降级到月）",
			expression: "D20250630",
			wantErr:    false,
			expectQuery: &TimeQuery{
				MonthNodes:  []string{"202506"},
				Granularity: "month",
				IsDegraded:  true,
			},
		},
		{
			name:       "相对时间-最近6个月",
			expression: "R6M",
			wantErr:    false,
			expectQuery: &TimeQuery{
				MonthNodes:  []string{"202502", "202503", "202504", "202505", "202506", "202507"},
				Granularity: "month",
				IsDegraded:  false,
			},
		},
		{
			name:       "相对时间-最近3年",
			expression: "R3Y",
			wantErr:    false,
			expectQuery: &TimeQuery{
				YearNodes:   []int{2023, 2024, 2025},
				Granularity: "year",
				IsDegraded:  false,
			},
		},
		{
			name:       "月份范围",
			expression: "M202501_M202503",
			wantErr:    false,
			expectQuery: &TimeQuery{
				MonthNodes:  []string{"202501", "202502", "202503"},
				Granularity: "month",
				IsDegraded:  false,
			},
		},
		{
			name:       "多个年份",
			expression: "Y2023,Y2024",
			wantErr:    false,
			expectQuery: &TimeQuery{
				YearNodes:   []int{2023, 2024},
				Granularity: "year",
				IsDegraded:  false,
			},
		},
		{
			name:       "季度表达式",
			expression: "Q2024Q1",
			wantErr:    false,
			expectQuery: &TimeQuery{
				MonthNodes:  []string{"202401", "202402", "202403"},
				Granularity: "month",
				IsDegraded:  false,
			},
		},
		{
			name:       "全年按季度",
			expression: "AY2024_Q",
			wantErr:    false,
			expectQuery: &TimeQuery{
				MonthNodes:  []string{"202401", "202402", "202403", "202404", "202405", "202406", "202407", "202408", "202409", "202410", "202411", "202412"},
				Granularity: "month",
				IsDegraded:  false,
			},
		},
		{
			name:       "无效表达式",
			expression: "INVALID",
			wantErr:    true,
		},
		{
			name:       "空表达式",
			expression: "",
			wantErr:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := parser.Parse(tt.expression)
			
			if tt.wantErr {
				if err == nil {
					t.Errorf("Parse() expected error but got none")
				}
				return
			}
			
			if err != nil {
				t.Errorf("Parse() unexpected error: %v", err)
				return
			}
			
			if result == nil {
				t.Errorf("Parse() returned nil result")
				return
			}
			
			// 检查结果
			if result.Granularity != tt.expectQuery.Granularity {
				t.Errorf("Parse() granularity = %v, want %v", result.Granularity, tt.expectQuery.Granularity)
			}
			
			if result.IsDegraded != tt.expectQuery.IsDegraded {
				t.Errorf("Parse() isDegraded = %v, want %v", result.IsDegraded, tt.expectQuery.IsDegraded)
			}
			
			// 检查年份节点
			if len(result.YearNodes) != len(tt.expectQuery.YearNodes) {
				t.Errorf("Parse() yearNodes length = %v, want %v", len(result.YearNodes), len(tt.expectQuery.YearNodes))
			} else {
				for i, year := range result.YearNodes {
					if year != tt.expectQuery.YearNodes[i] {
						t.Errorf("Parse() yearNodes[%d] = %v, want %v", i, year, tt.expectQuery.YearNodes[i])
					}
				}
			}
			
			// 检查月份节点
			if len(result.MonthNodes) != len(tt.expectQuery.MonthNodes) {
				t.Errorf("Parse() monthNodes length = %v, want %v", len(result.MonthNodes), len(tt.expectQuery.MonthNodes))
			} else {
				for i, month := range result.MonthNodes {
					if month != tt.expectQuery.MonthNodes[i] {
						t.Errorf("Parse() monthNodes[%d] = %v, want %v", i, month, tt.expectQuery.MonthNodes[i])
					}
				}
			}
		})
	}
}

func TestCSTELParser_QuarterToMonths(t *testing.T) {
	parser := NewCSTELParser()
	
	tests := []struct {
		name     string
		year     int
		quarter  int
		expected []string
	}{
		{
			name:     "2024年第1季度",
			year:     2024,
			quarter:  1,
			expected: []string{"202401", "202402", "202403"},
		},
		{
			name:     "2024年第2季度",
			year:     2024,
			quarter:  2,
			expected: []string{"202404", "202405", "202406"},
		},
		{
			name:     "2024年第3季度",
			year:     2024,
			quarter:  3,
			expected: []string{"202407", "202408", "202409"},
		},
		{
			name:     "2024年第4季度",
			year:     2024,
			quarter:  4,
			expected: []string{"202410", "202411", "202412"},
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := parser.quarterToMonths(tt.year, tt.quarter)
			
			if len(result) != len(tt.expected) {
				t.Errorf("quarterToMonths() length = %v, want %v", len(result), len(tt.expected))
				return
			}
			
			for i, month := range result {
				if month != tt.expected[i] {
					t.Errorf("quarterToMonths()[%d] = %v, want %v", i, month, tt.expected[i])
				}
			}
		})
	}
}

func TestCSTELParser_GenerateMonthRange(t *testing.T) {
	parser := NewCSTELParser()
	
	tests := []struct {
		name     string
		startYM  string
		endYM    string
		expected []string
		wantErr  bool
	}{
		{
			name:     "正常月份范围",
			startYM:  "202501",
			endYM:    "202503",
			expected: []string{"202501", "202502", "202503"},
			wantErr:  false,
		},
		{
			name:     "跨年月份范围",
			startYM:  "202412",
			endYM:    "202502",
			expected: []string{"202412", "202501", "202502"},
			wantErr:  false,
		},
		{
			name:     "单个月份",
			startYM:  "202506",
			endYM:    "202506",
			expected: []string{"202506"},
			wantErr:  false,
		},
		{
			name:    "开始月份晚于结束月份",
			startYM: "202506",
			endYM:   "202505",
			wantErr: true,
		},
		{
			name:    "无效开始月份",
			startYM: "invalid",
			endYM:   "202506",
			wantErr: true,
		},
		{
			name:    "无效结束月份",
			startYM: "202505",
			endYM:   "invalid",
			wantErr: true,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := parser.generateMonthRange(tt.startYM, tt.endYM)
			
			if tt.wantErr {
				if err == nil {
					t.Errorf("generateMonthRange() expected error but got none")
				}
				return
			}
			
			if err != nil {
				t.Errorf("generateMonthRange() unexpected error: %v", err)
				return
			}
			
			if len(result) != len(tt.expected) {
				t.Errorf("generateMonthRange() length = %v, want %v", len(result), len(tt.expected))
				return
			}
			
			for i, month := range result {
				if month != tt.expected[i] {
					t.Errorf("generateMonthRange()[%d] = %v, want %v", i, month, tt.expected[i])
				}
			}
		})
	}
}
