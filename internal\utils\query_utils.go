package utils

import "strings"

// GetStringValue 安全获取字符串值
func GetStringValue(value interface{}) string {
	if value == nil {
		return ""
	}
	if str, ok := value.(string); ok {
		return str
	}
	return ""
}

// GetFloatValue 安全获取浮点数值
func GetFloatValue(value interface{}) float64 {
	if value == nil {
		return 0
	}
	if f, ok := value.(float64); ok {
		return f
	}
	if i, ok := value.(int64); ok {
		return float64(i)
	}
	return 0
}

// GetIntValue 安全获取整数值
func GetIntValue(value interface{}) int {
	if value == nil {
		return 0
	}
	if i, ok := value.(int64); ok {
		return int(i)
	}
	if i, ok := value.(int); ok {
		return i
	}
	return 0
}

// RemoveEntitySuffixes 去除实体名称的常见后缀
func RemoveEntitySuffixes(name string, suffixes []string) string {
	cleanedName := name

	// 尝试去除每个后缀
	for _, suffix := range suffixes {
		if strings.HasSuffix(cleanedName, suffix) {
			cleanedName = strings.TrimSuffix(cleanedName, suffix)
			break // 只去除第一个匹配的后缀
		}
	}

	// 去除前后空格
	cleanedName = strings.TrimSpace(cleanedName)

	// 如果去除后缀后名称为空，返回原名称
	if cleanedName == "" {
		return name
	}

	return cleanedName
}

// HasFilters 检查是否有过滤器
func HasFilters(filters interface{}) bool {
	if filters == nil {
		return false
	}

	filterMap, ok := filters.(map[string]interface{})
	if !ok {
		return false
	}

	return len(filterMap) > 0
}
