﻿package main

import (
"context"
"log"
"net/http"
"os"
"os/signal"
"syscall"
"time"

"github.com/neo4j/neo4j-go-driver/v5/neo4j"
"gopkg.in/yaml.v2"
"neoapi/internal/router"
)

type Config struct {
Server struct {
Port string `yaml:"port"`
} `yaml:"server"`
Database struct {
URI      string `yaml:"uri"`
Username string `yaml:"username"`
Password string `yaml:"password"`
} `yaml:"database"`
}

func main() {
log.Println("Starting NeoAPI server...")

log.Println("Loading configuration...")
config, err := loadConfig("config.yaml")
if err != nil {
log.Fatalf("Failed to load config: %v", err)
}
log.Println("Configuration loaded successfully")

log.Println("Creating Neo4j driver...")
driver, err := neo4j.NewDriverWithContext(
config.Database.URI,
neo4j.BasicAuth(config.Database.Username, config.Database.Password, ""),
)
if err != nil {
log.Fatalf("Failed to create Neo4j driver: %v", err)
}
defer driver.Close(context.Background())
log.Println("Neo4j driver created successfully")

ctx := context.Background()
err = driver.VerifyConnectivity(ctx)
if err != nil {
	log.Fatalf("Failed to connect to Neo4j: %v", err)
}
log.Println("Successfully connected to Neo4j")

r := router.SetupRouter(driver)

port := config.Server.Port
if port == "" {
port = "8080"
}

srv := &http.Server{
Addr:    ":" + port,
Handler: r,
}

go func() {
if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
log.Fatalf("Failed to start server: %v", err)
}
}()

log.Printf("NeoAPI v3.1 server started on port %s", port)

quit := make(chan os.Signal, 1)
signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
<-quit
log.Println("Shutting down server...")

ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
defer cancel()
if err := srv.Shutdown(ctx); err != nil {
log.Fatal("Server forced to shutdown:", err)
}

log.Println("Server exited")
}

func loadConfig(filename string) (*Config, error) {
file, err := os.Open(filename)
if err != nil {
return nil, err
}
defer file.Close()

var config Config
decoder := yaml.NewDecoder(file)
err = decoder.Decode(&config)
if err != nil {
return nil, err
}

return &config, nil
}