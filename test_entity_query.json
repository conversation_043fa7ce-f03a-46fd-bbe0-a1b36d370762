{"test_cases": [{"name": "基础船舶搜索", "description": "搜索名称包含'汉海'的船舶", "request": {"entity_type": "Ship", "time_expression": "M202407", "filters": {"name": "汉海"}, "limit": 10}}, {"name": "船舶属性过滤", "description": "按船舶属性过滤", "request": {"entity_type": "Ship", "time_expression": "M202407", "filters": {"dwt_min": 3000, "dwt_max": 5000, "ship_types": ["干散货船"]}, "limit": 20}}, {"name": "复合条件查询", "description": "核心需求场景：在武汉-南京航线运输煤炭的3000-5000吨级船舶", "request": {"entity_type": "Ship", "time_expression": "M202407", "filters": {"dwt_min": 3000, "dwt_max": 5000, "active_routes": ["武汉-南京航线"], "cargo_types": ["煤炭及制品"], "min_voyages": 3, "min_load_ratio": 0.7}, "limit": 10}}, {"name": "航线查询", "description": "查找从武汉出发的干线航线", "request": {"entity_type": "ShippingRoute", "time_expression": "M202407", "filters": {"origin_ports": ["武汉"], "route_types": ["干线"], "distance_min": 200, "distance_max": 800, "min_ships": 10}, "limit": 15}}, {"name": "港口查询", "description": "查找湖北省的港口", "request": {"entity_type": "Port", "time_expression": "M202407", "filters": {"provinces": ["湖北省"], "min_cargo_volume": 100000}, "limit": 10}}, {"name": "纯排名查询", "description": "无过滤条件的纯排名查询", "request": {"entity_type": "Ship", "time_expression": "M202407", "limit": 20}}]}