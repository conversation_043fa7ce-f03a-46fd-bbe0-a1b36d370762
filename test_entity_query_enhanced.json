{"test_cases": [{"name": "基础船舶搜索 - 精确匹配", "description": "搜索名称包含'汉海'的船舶，验证模糊匹配功能", "request": {"entity_type": "Ship", "time_expression": "R2M", "filters": {"name": "汉海"}, "limit": 10}, "expected_results": {"min_results": 1, "should_contain": ["汉海5号"]}}, {"name": "集装箱船舶搜索", "description": "查找集装箱船舶类型，基于实际数据中的船舶类型", "request": {"entity_type": "Ship", "time_expression": "R2M", "filters": {"ship_types": ["集装箱船"]}, "limit": 20}, "expected_results": {"min_results": 1, "should_contain": ["汉海5号"]}}, {"name": "载重吨范围过滤 - 精确范围", "description": "查找载重吨在15000-18000吨之间的船舶", "request": {"entity_type": "Ship", "time_expression": "R2M", "filters": {"dwt_min": 15000, "dwt_max": 18000}, "limit": 20}, "expected_results": {"min_results": 1, "description": "汉海5号载重16800吨，应在范围内"}}, {"name": "运营船舶航次过滤", "description": "查找航次数大于等于10次的活跃船舶", "request": {"entity_type": "Ship", "time_expression": "R2M", "filters": {"min_voyages": 10}, "limit": 10}, "expected_results": {"min_results": 1, "description": "汉海5号有12次航次，应满足条件"}}, {"name": "服务特定港口的船舶", "description": "查找服务过武汉、上海、太仓等港口的船舶", "request": {"entity_type": "Ship", "time_expression": "R2M", "filters": {"served_ports": ["武汉", "上海", "太仓"]}, "limit": 15}, "expected_results": {"min_results": 1, "description": "汉海5号的常用港口包含这些港口"}}, {"name": "运输集装箱货物的船舶", "description": "查找运输过集装箱货运量的船舶", "request": {"entity_type": "Ship", "time_expression": "R2M", "filters": {"transported_cargos": ["集装箱货运量"]}, "limit": 10}, "expected_results": {"min_results": 1, "description": "汉海5号主要运输集装箱货运量(99.17%)"}}, {"name": "运营特定航线的船舶", "description": "查找运营上海-太仓航线或九江-武汉航线的船舶", "request": {"entity_type": "Ship", "time_expression": "R2M", "filters": {"active_routes": ["上海-太仓航线", "九江-武汉航线"]}, "limit": 10}, "expected_results": {"min_results": 1, "description": "汉海5号运营这些航线"}}, {"name": "复合条件查询 - 集装箱船运营状况", "description": "核心业务场景：查找载重15000吨以上、运营长江主要航线、运输集装箱货物的船舶", "request": {"entity_type": "Ship", "time_expression": "R2M", "filters": {"dwt_min": 15000, "ship_types": ["集装箱船"], "transported_cargos": ["集装箱货运量"], "served_ports": ["武汉", "上海"], "min_voyages": 8}, "limit": 5}, "expected_results": {"min_results": 1, "description": "汉海5号完全符合所有条件"}}, {"name": "湖北省港口查询", "description": "查找湖北省的主要港口", "request": {"entity_type": "Port", "time_expression": "R2M", "filters": {"provinces": ["湖北省"]}, "limit": 10}, "expected_results": {"min_results": 1, "should_contain": ["武汉"]}}, {"name": "高吞吐量港口查询", "description": "查找月货运量超过1000万吨的港口", "request": {"entity_type": "Port", "time_expression": "R2M", "filters": {"min_cargo_volume": 10000000}, "limit": 10}, "expected_results": {"min_results": 2, "description": "武汉港和南京港都超过1000万吨"}}, {"name": "长江流域港口对比", "description": "查找长江流域的主要港口进行对比分析", "request": {"entity_type": "Port", "time_expression": "R2M", "filters": {"provinces": ["湖北省", "江苏省"]}, "limit": 10}, "expected_results": {"min_results": 2, "should_contain": ["武汉", "南京"]}}, {"name": "船舶运营绩效排名", "description": "无过滤条件的船舶排名，按运营指标排序", "request": {"entity_type": "Ship", "time_expression": "R2M", "limit": 20}, "expected_results": {"min_results": 1, "description": "返回所有船舶的运营排名"}}, {"name": "特定时间段的船舶表现", "description": "查询2024年7月船舶运营数据", "request": {"entity_type": "Ship", "time_expression": "M202407", "filters": {"min_voyages": 1}, "limit": 10}, "expected_results": {"min_results": 0, "description": "2024年7月可能没有数据"}}, {"name": "化工原料运输船舶", "description": "查找运输过化工原料及制品的船舶", "request": {"entity_type": "Ship", "time_expression": "R2M", "filters": {"transported_cargos": ["化工原料及制品"], "min_cargo_volume": 200}, "limit": 10}, "expected_results": {"min_results": 1, "description": "汉海5号运输过317.49吨化工原料及制品"}}, {"name": "多货物类型运输船舶", "description": "查找运输多种货物类型的多用途船舶", "request": {"entity_type": "Ship", "time_expression": "R2M", "filters": {"transported_cargos": ["集装箱货运量", "化工原料及制品"]}, "limit": 10}, "expected_results": {"min_results": 1, "description": "汉海5号运输两种货物类型"}}], "advanced_test_scenarios": [{"name": "船舶画像深度分析", "description": "获取船舶的完整运营画像，包括历史表现和航线分析", "api_endpoint": "/api/query", "request": {"query_type": "PROFILE", "entity": {"type": "Ship", "identifier": "汉海5号", "resolution_strategy": "fuzzy"}, "metric": "船舶画像", "time_expression": "R2M"}, "validation_points": ["基础信息完整性", "历史表现数据", "货物构成分析", "航线运营分析", "性能评级"]}, {"name": "港口对比分析", "description": "对比武汉港和南京港的吞吐量表现", "api_endpoint": "/api/query", "request": {"query_type": "COMPARE", "entity": {"type": "Port", "identifier": ["武汉", "南京"]}, "metric": "总吞吐量", "time_expression": "R2M"}, "validation_points": ["对比数据准确性", "统计摘要完整性", "时间序列数据"]}, {"name": "实体搜索功能验证", "description": "验证实体搜索的准确性和相关性评分", "api_endpoint": "/api/search/entities", "request": {"query": "汉海", "entity_type": "Ship", "strategy": "fuzzy", "limit": 10}, "validation_points": ["搜索准确性", "相关性评分", "匹配类型", "描述信息完整性"]}], "performance_benchmarks": {"response_time_targets": {"simple_query": "< 50ms", "complex_filter": "< 200ms", "profile_query": "< 2000ms", "search_query": "< 100ms"}, "data_quality_requirements": {"entity_resolution_accuracy": "> 95%", "null_value_handling": "graceful_fallback", "data_completeness": "> 90%"}}}