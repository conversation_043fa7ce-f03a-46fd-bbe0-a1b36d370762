﻿# NeoAPI 综合测试脚本 (PowerShell版本)
# 涵盖所有核心功能：基础API、画像接口、比较查询、空值处理、高级分析等
#
# 最近更新：
# - 添加了搜索接口的全面测试（GET和POST方法）
# - 新增实用功能测试分类，验证辅助函数和内部功能
# - 测试数据类型处理的正确性（getStringValue, getFloatValue, getIntValue）
# - 预留搜索过滤器功能的测试用例

param(
    [string]$ApiBase = "http://localhost:8080",
    [string]$ShipName = "汉海5号",
    [string]$PortName = "武汉",
    [string]$CompareShipName = "汉海6号",
    [string]$ComparePortName = "张家港",
    [string]$ThirdPortName = "万州",
    [switch]$BasicOnly,
    [switch]$ProfileOnly,
    [switch]$ComparisonOnly,
    [switch]$AdvancedOnly,
    [switch]$UtilityOnly,
    [switch]$BusinessOnly,
    [switch]$QuickMode,
    [switch]$FastMode,
    [switch]$Verbose,
    [switch]$Help
)

# 显示帮助信息
if ($Help) {
    Write-Host "NeoAPI 综合测试脚本 (PowerShell版本)" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "用法: .\test_neoapi_comprehensive.ps1 [参数]"
    Write-Host ""
    Write-Host "参数:"
    Write-Host "  -ApiBase <url>          API服务地址 (默认: http://localhost:8080)"
    Write-Host "  -ShipName <name>        测试船舶名称 (默认: 汉海5号)"
    Write-Host "  -PortName <name>        测试港口名称 (默认: 武汉)"
    Write-Host "  -CompareShipName <name> 比较船舶名称 (默认: 汉海6号)"
    Write-Host "  -ComparePortName <name> 比较港口名称 (默认: 张家港)"
    Write-Host "  -BasicOnly              仅运行基础功能测试"
    Write-Host "  -ProfileOnly            仅运行画像接口测试"
    Write-Host "  -ComparisonOnly         仅运行比较查询测试"
    Write-Host "  -AdvancedOnly           仅运行高级分析测试"
    Write-Host "  -UtilityOnly            仅运行实用功能测试"
    Write-Host "  -BusinessOnly           仅运行业务场景测试"
    Write-Host "  -QuickMode              快速测试（跳过耗时测试）"
    Write-Host "  -FastMode               快速模式（减少输出）"
    Write-Host "  -Verbose                详细输出模式"
    Write-Host "  -Help                   显示此帮助信息"
    exit 0
}

# 加载System.Web程序集用于URL编码
Add-Type -AssemblyName System.Web

# 颜色函数
function Write-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Write-Error { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }
function Write-Info { param($Message) Write-Host "ℹ️ $Message" -ForegroundColor Blue }
function Write-Warning { param($Message) Write-Host "⚠️ $Message" -ForegroundColor Yellow }
function Write-Category { param($Message) Write-Host "$Message" -ForegroundColor Magenta }

# 测试计数器
$global:TotalTests = 0
$global:PassedTests = 0
$global:BasicTests = 0
$global:BasicPassed = 0
$global:ProfileTests = 0
$global:ProfilePassed = 0
$global:ComparisonTests = 0
$global:ComparisonPassed = 0
$global:AdvancedTests = 0
$global:AdvancedPassed = 0
$global:UtilityTests = 0
$global:UtilityPassed = 0
$global:BusinessTests = 0
$global:BusinessPassed = 0

# 测试函数
function Test-API {
    param(
        [string]$Category,
        [string]$Name,
        [string]$Method = "GET",
        [string]$Uri,
        [hashtable]$Body = $null,
        [int]$ExpectedStatusCode = 200
    )

    $global:TotalTests++

    switch ($Category) {
        "BASIC" { $global:BasicTests++ }
        "PROFILE" { $global:ProfileTests++ }
        "COMPARISON" { $global:ComparisonTests++ }
        "ADVANCED" { $global:AdvancedTests++ }
        "UTILITY" { $global:UtilityTests++ }
        "BUSINESS" { $global:BusinessTests++ }
    }

    Write-Info "[$global:TotalTests] [$Category] $Name"

    # URL编码处理，确保中文字符正确编码
    if ($Method -eq "GET") {
        $encodedUri = [System.Web.HttpUtility]::UrlPathEncode($Uri)
        Write-Host "   请求URL: $encodedUri" -ForegroundColor Gray
    }

    try {
        if ($Method -eq "GET") {
            # 使用正确的URL编码
            $response = Invoke-WebRequest -Uri $Uri -Method $Method -UseBasicParsing -ErrorAction Stop
        } else {
            $jsonBody = $Body | ConvertTo-Json -Depth 10
            $response = Invoke-WebRequest -Uri $Uri -Method $Method -Body $jsonBody -ContentType "application/json" -UseBasicParsing -ErrorAction Stop
        }

        if ($response.StatusCode -eq $ExpectedStatusCode) {
            Write-Success "通过 (状态码: $($response.StatusCode))"
            $global:PassedTests++

            switch ($Category) {
                "BASIC" { $global:BasicPassed++ }
                "PROFILE" { $global:ProfilePassed++ }
                "COMPARISON" { $global:ComparisonPassed++ }
                "ADVANCED" { $global:AdvancedPassed++ }
                "UTILITY" { $global:UtilityPassed++ }
                "BUSINESS" { $global:BusinessPassed++ }
            }

            # 显示关键信息
            if ($ExpectedStatusCode -eq 200) {
                try {
                    $jsonResponse = $response.Content | ConvertFrom-Json
                    if ($jsonResponse.data -and $jsonResponse.data.comparison) {
                        $comparisonCount = $jsonResponse.data.comparison.Count
                        Write-Host "   比较结果: $comparisonCount 个实体" -ForegroundColor Gray
                    } elseif ($jsonResponse.basic_info) {
                        Write-Host "   画像实体: $($jsonResponse.basic_info.name)" -ForegroundColor Gray
                    } elseif ($jsonResponse.results) {
                        Write-Host "   搜索结果: $($jsonResponse.total) 个" -ForegroundColor Gray
                    } elseif ($jsonResponse.status) {
                        Write-Host "   服务状态: $($jsonResponse.status)" -ForegroundColor Gray
                    } else {
                        Write-Host "   响应正常" -ForegroundColor Gray
                    }
                } catch {
                    Write-Host "   响应正常" -ForegroundColor Gray
                }
            }
        }
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        if ($statusCode -eq $ExpectedStatusCode) {
            Write-Success "通过 (状态码: $statusCode)"
            $global:PassedTests++

            switch ($Category) {
                "BASIC" { $global:BasicPassed++ }
                "PROFILE" { $global:ProfilePassed++ }
                "COMPARISON" { $global:ComparisonPassed++ }
                "ADVANCED" { $global:AdvancedPassed++ }
                "UTILITY" { $global:UtilityPassed++ }
                "BUSINESS" { $global:BusinessPassed++ }
            }
        } else {
            Write-Error "失败 (期望: $ExpectedStatusCode, 实际: $statusCode)"
            Write-Host "   错误: $($_.Exception.Message)" -ForegroundColor Red

            # 尝试获取响应体中的错误信息
            try {
                if ($_.Exception.Response) {
                    $stream = $_.Exception.Response.GetResponseStream()
                    $reader = New-Object System.IO.StreamReader($stream)
                    $responseBody = $reader.ReadToEnd()
                    $reader.Close()

                    if ($responseBody) {
                        Write-Host "   响应体: $responseBody" -ForegroundColor Red

                        # 尝试解析JSON错误信息
                        try {
                            $errorJson = $responseBody | ConvertFrom-Json
                            if ($errorJson.error) {
                                Write-Host "   API错误: $($errorJson.error)" -ForegroundColor Red
                            }
                            if ($errorJson.message) {
                                Write-Host "   错误消息: $($errorJson.message)" -ForegroundColor Red
                            }
                        } catch {
                            # JSON解析失败，显示原始响应
                        }
                    }
                }
            } catch {
                Write-Host "   无法获取详细错误信息" -ForegroundColor Red
            }
        }
    }

    Write-Host ""
}

# 显示测试开始信息
Write-Host "========================================"
Write-Host "🚀 NeoAPI 综合测试套件 (PowerShell版本)"
Write-Host "========================================"
Write-Host "API地址: $ApiBase"
Write-Host "测试船舶: $ShipName"
Write-Host "比较船舶: $CompareShipName"
Write-Host "测试港口: $PortName"
Write-Host "比较港口: $ComparePortName, $ThirdPortName"
Write-Host ""
Write-Host "测试模式:"
if ($BasicOnly) {
    Write-Host "  🔹 仅基础功能测试"
} elseif ($ProfileOnly) {
    Write-Host "  🔹 仅画像接口测试"
} elseif ($ComparisonOnly) {
    Write-Host "  🔹 仅比较查询测试"
} elseif ($AdvancedOnly) {
    Write-Host "  🔹 仅高级分析测试"
} elseif ($UtilityOnly) {
    Write-Host "  🔹 仅实用功能测试"
} elseif ($BusinessOnly) {
    Write-Host "  🔹 仅业务场景测试"
} else {
    Write-Host "  🔹 完整测试套件"
}
if ($QuickMode) {
    Write-Host "  ⚡ 快速模式"
}
if ($Verbose) {
    Write-Host "  📝 详细输出"
}
Write-Host "========================================"

# 基础功能测试
if ($BasicOnly -or (-not $ProfileOnly -and -not $ComparisonOnly -and -not $AdvancedOnly -and -not $UtilityOnly -and -not $BusinessOnly)) {
    Write-Category "`n=== 🔧 基础功能测试 ==="

    Test-API -Category "BASIC" -Name "API健康检查" -Uri "$ApiBase/api/health"
    Test-API -Category "BASIC" -Name "元数据-实体类型" -Uri "$ApiBase/api/metadata/ship_types"
    Test-API -Category "BASIC" -Name "搜索接口-GET方法" -Uri "$ApiBase/api/search/entities?q=汉海&type=Ship&strategy=fuzzy&limit=5"

    # 测试搜索接口的各种参数组合
    Test-API -Category "BASIC" -Name "搜索接口-精确匹配" -Uri "$ApiBase/api/search/entities?q=汉海5号&type=Ship&strategy=exact&limit=1"
    Test-API -Category "BASIC" -Name "搜索接口-港口搜索" -Uri "$ApiBase/api/search/entities?q=武汉&type=Port&strategy=fuzzy&limit=10"
    Test-API -Category "BASIC" -Name "搜索接口-省份搜索" -Uri "$ApiBase/api/search/entities?q=湖北&type=Province&strategy=fuzzy&limit=5"
    Test-API -Category "BASIC" -Name "搜索接口-流域搜索" -Uri "$ApiBase/api/search/entities?q=长江&type=Basin&strategy=fuzzy&limit=3"
    Test-API -Category "BASIC" -Name "搜索接口-航线搜索" -Uri "$ApiBase/api/search/entities?q=武汉&type=ShippingRoute&strategy=fuzzy&limit=5"

    # 测试POST方法的搜索接口
    Test-API -Category "BASIC" -Name "搜索接口-POST方法基础搜索" -Method "POST" -Uri "$ApiBase/api/search/entities" -Body @{
        query = "汉海"
        entity_type = "Ship"
        strategy = "fuzzy"
        limit = 5
    }

    Test-API -Category "BASIC" -Name "搜索接口-POST方法多实体搜索" -Method "POST" -Uri "$ApiBase/api/search/entities" -Body @{
        query = "武汉"
        strategy = "fuzzy"
        limit = 20
    }

    Test-API -Category "BASIC" -Name "搜索接口-POST方法精确匹配" -Method "POST" -Uri "$ApiBase/api/search/entities" -Body @{
        query = $ShipName
        entity_type = "Ship"
        strategy = "exact"
        limit = 1
    }

    Test-API -Category "BASIC" -Name "统一查询-POINT查询" -Method "POST" -Uri "$ApiBase/api/query" -Body @{
        query_type = "POINT"
        entity = @{
            type = "Ship"
            identifier = $ShipName
        }
        metric = "有效营运率"
        time_expression = "M202407"
    }

    Test-API -Category "BASIC" -Name "核心查询-POINT查询" -Method "POST" -Uri "$ApiBase/api/core/query" -Body @{
        query_type = "POINT"
        entity = @{
            type = "Ship"
            identifier = $ShipName
        }
        metric = "载货量"
        time_expression = "M202407"
    }
}

# 画像接口测试
if ($ProfileOnly -or (-not $BasicOnly -and -not $ComparisonOnly -and -not $AdvancedOnly -and -not $UtilityOnly -and -not $BusinessOnly)) {
    # Write-Category "`n=== 👤 画像接口测试 ==="

    # Test-API -Category "PROFILE" -Name "船舶画像-基本信息" -Uri "$ApiBase/api/profiles/ships/$ShipName?time_expression=R6M&detail_level=basic"
    # Test-API -Category "PROFILE" -Name "船舶画像-详细信息" -Uri "$ApiBase/api/profiles/ships/$ShipName?time_expression=R6M&detail_level=detailed&include_history=true&include_analysis=true"
    # Test-API -Category "PROFILE" -Name "港口画像-基本信息" -Uri "$ApiBase/api/profiles/ports/$encodedPortName?time_expression=R6M&detail_level=basic"
    # Test-API -Category "PROFILE" -Name "港口画像-详细信息" -Uri "$ApiBase/api/profiles/ports/$encodedPortName?time_expression=R6M&detail_level=detailed&include_history=true&include_cargo=true"

    # 测试统一查询接口（推荐方式）
    Write-Info "测试统一查询接口..."
    Test-API -Category "PROFILE" -Name "船舶画像-统一查询接口" -Method "POST" -Uri "$ApiBase/api/query" -Body @{
        query_type = "PROFILE"
        entity = @{
            type = "Ship"
            identifier = $ShipName
            resolution_strategy = "fuzzy"
        }
        metric = "船舶画像"
        time_expression = "R6M"
    }

    Test-API -Category "PROFILE" -Name "港口画像-统一查询接口" -Method "POST" -Uri "$ApiBase/api/query" -Body @{
        query_type = "PROFILE"
        entity = @{
            type = "Port"
            identifier = $PortName
            resolution_strategy = "fuzzy"
        }
        metric = "港口画像"
        time_expression = "R6M"
    }
}

# 比较查询测试
if ($ComparisonOnly -or (-not $BasicOnly -and -not $ProfileOnly -and -not $AdvancedOnly -and -not $UtilityOnly -and -not $BusinessOnly)) {
    Write-Category "`n=== ⚖️ 比较查询测试 ==="

    Test-API -Category "COMPARISON" -Name "船舶货运量比较" -Method "POST" -Uri "$ApiBase/api/analytics/query" -Body @{
        query_type = "COMPARE"
        entity = @{
            type = "Ship"
            identifier = @($ShipName, $CompareShipName)
        }
        metric = "载货量"
        time_expression = "R6M"

    }

    Test-API -Category "COMPARISON" -Name "港口吞吐量比较" -Method "POST" -Uri "$ApiBase/api/analytics/query" -Body @{
        query_type = "COMPARE"
        entity = @{
            type = "Port"
            identifier = @($PortName, $ComparePortName)
        }
        metric = "总吞吐量"
        time_expression = "R6M"

    }
}

# 高级分析测试
if ($AdvancedOnly -or (-not $BasicOnly -and -not $ProfileOnly -and -not $ComparisonOnly -and -not $UtilityOnly -and -not $BusinessOnly)) {
    Write-Category "`n=== 🔬 高级分析测试 ==="

    Test-API -Category "ADVANCED" -Name "港口排名查询" -Method "POST" -Uri "$ApiBase/api/analytics/query" -Body @{
        query_type = "RANK"
        entity = @{
            type = "Port"
        }
        metric = "总吞吐量"
        time_expression = "R6M"
        limit = 20
    }

    Test-API -Category "ADVANCED" -Name "船舶性能基准比较" -Method "POST" -Uri "$ApiBase/api/analytics/query" -Body @{
        query_type = "COMPARE"
        entity = @{
            type = "Ship"
            identifier = @($ShipName, $CompareShipName)
        }
        metric = "载货量"
        time_expression = "R6M"
    }
}

# 实用功能测试 - 测试辅助函数和内部功能的正确性
if ($UtilityOnly -or (-not $BasicOnly -and -not $ProfileOnly -and -not $ComparisonOnly -and -not $AdvancedOnly -and -not $BusinessOnly)) {
    Write-Category "`n=== 🔧 实用功能测试 ==="

    # 测试搜索功能的边界情况
    Test-API -Category "UTILITY" -Name "搜索-空查询处理" -Method "POST" -Uri "$ApiBase/api/search/entities" -Body @{
        query = ""
        entity_type = "Ship"
        strategy = "fuzzy"
        limit = 5
    } -ExpectedStatusCode 400

    Test-API -Category "UTILITY" -Name "搜索-无效实体类型" -Method "POST" -Uri "$ApiBase/api/search/entities" -Body @{
        query = "测试"
        entity_type = "InvalidType"
        strategy = "fuzzy"
        limit = 5
    }

    Test-API -Category "UTILITY" -Name "搜索-超大限制值" -Method "POST" -Uri "$ApiBase/api/search/entities" -Body @{
        query = "汉海"
        entity_type = "Ship"
        strategy = "fuzzy"
        limit = 1000
    }

    Test-API -Category "UTILITY" -Name "搜索-零限制值" -Method "POST" -Uri "$ApiBase/api/search/entities" -Body @{
        query = "汉海"
        entity_type = "Ship"
        strategy = "fuzzy"
        limit = 0
    }

    # 测试不同搜索策略的一致性
    Test-API -Category "UTILITY" -Name "搜索策略-精确vs模糊对比" -Method "POST" -Uri "$ApiBase/api/search/entities" -Body @{
        query = $ShipName
        entity_type = "Ship"
        strategy = "exact"
        limit = 1
    }

    # 测试各实体类型的搜索功能
    Test-API -Category "UTILITY" -Name "实体搜索-船舶类型完整性" -Method "POST" -Uri "$ApiBase/api/search/entities" -Body @{
        query = "船"
        entity_type = "Ship"
        strategy = "fuzzy"
        limit = 10
    }

    Test-API -Category "UTILITY" -Name "实体搜索-港口类型完整性" -Method "POST" -Uri "$ApiBase/api/search/entities" -Body @{
        query = "港"
        entity_type = "Port"
        strategy = "fuzzy"
        limit = 10
    }

    # 测试数据类型处理的正确性（通过查询结果验证）
    Test-API -Category "UTILITY" -Name "数据类型-字符串值处理" -Method "POST" -Uri "$ApiBase/api/query" -Body @{
        query_type = "POINT"
        entity = @{
            type = "Ship"
            identifier = $ShipName
            resolution_strategy = "fuzzy"
        }
        metric = "船舶名称"
        time_expression = "M202407"
    }

    Test-API -Category "UTILITY" -Name "数据类型-数值处理" -Method "POST" -Uri "$ApiBase/api/query" -Body @{
        query_type = "POINT"
        entity = @{
            type = "Ship"
            identifier = $ShipName
            resolution_strategy = "fuzzy"
        }
        metric = "载货量"
        time_expression = "M202407"
    }

    Test-API -Category "UTILITY" -Name "数据类型-整数值处理" -Method "POST" -Uri "$ApiBase/api/query" -Body @{
        query_type = "POINT"
        entity = @{
            type = "Ship"
            identifier = $ShipName
            resolution_strategy = "fuzzy"
        }
        metric = "航次数"
        time_expression = "M202407"
    }

    # 预留：测试搜索过滤器功能（未来实现）
    Write-Info "预留测试：搜索过滤器功能（计划实现）"
    # Test-API -Category "UTILITY" -Name "搜索过滤器-船舶类型过滤" -Method "POST" -Uri "$ApiBase/api/search/entities" -Body @{
    #     query = "汉海"
    #     entity_type = "Ship"
    #     strategy = "fuzzy"
    #     limit = 10
    #     filters = @{
    #         ship_type = "散货船"
    #     }
    # }

    # Test-API -Category "UTILITY" -Name "搜索过滤器-货物类型过滤" -Method "POST" -Uri "$ApiBase/api/search/entities" -Body @{
    #     query = "汉海"
    #     entity_type = "Ship"
    #     strategy = "fuzzy"
    #     limit = 10
    #     filters = @{
    #         cargo_type = "煤炭及制品"
    #     }
    # }

    # Test-API -Category "UTILITY" -Name "搜索过滤器-多重过滤" -Method "POST" -Uri "$ApiBase/api/search/entities" -Body @{
    #     query = "港"
    #     entity_type = "Port"
    #     strategy = "fuzzy"
    #     limit = 10
    #     filters = @{
    #         province = "湖北省"
    #         port_type = "内河港"
    #     }
    # }
}

# 业务场景测试 - 测试真实业务场景的复杂查询
if ($BusinessOnly -or (-not $BasicOnly -and -not $ProfileOnly -and -not $ComparisonOnly -and -not $AdvancedOnly -and -not $UtilityOnly)) {
    Write-Category "`n=== 🏢 业务场景测试 ==="

    # 1. 组合指标测试 (COMPOSE) - 货物构成分析
    Test-API -Category "BUSINESS" -Name "业务场景-船舶货物构成分析" -Method "POST" -Uri "$ApiBase/api/analytics/query" -Body @{
        query_type = "COMPOSE"
        entity = @{
            type = "Ship"
            identifier = $ShipName
            resolution_strategy = "fuzzy"
        }
        metric = "货物构成"
        time_expression = "R6M"
    }

    Test-API -Category "BUSINESS" -Name "业务场景-航线货物构成分析" -Method "POST" -Uri "$ApiBase/api/analytics/query" -Body @{
        query_type = "COMPOSE"
        entity = @{
            type = "ShippingRoute"
            identifier = "武汉-南京"
            resolution_strategy = "fuzzy"
        }
        metric = "货物构成"
        time_expression = "R3M"
    }

    # 2. 多维度统一查询 - 船舶、航线、货类
    Test-API -Category "BUSINESS" -Name "业务场景-船舶多维度查询" -Method "POST" -Uri "$ApiBase/api/query" -Body @{
        query_type = "POINT"
        entity = @{
            type = "Ship"
            identifier = $ShipName
            resolution_strategy = "fuzzy"
        }
        metric = "分货类运量"
        time_expression = "M202407"
        filters = @{
            cargo_type = "煤炭及制品"
            route = "武汉-南京"
        }
    }

    Test-API -Category "BUSINESS" -Name "业务场景-航线多维度查询" -Method "POST" -Uri "$ApiBase/api/query" -Body @{
        query_type = "POINT"
        entity = @{
            type = "ShippingRoute"
            identifier = "武汉-南京"
            resolution_strategy = "fuzzy"
        }
        metric = "航线货运量"
        time_expression = "M202407"
        filters = @{
            cargo_type = "金属矿石"
            ship_type = "散货船"
        }
    }

    Test-API -Category "BUSINESS" -Name "业务场景-货类运输分析" -Method "POST" -Uri "$ApiBase/api/query" -Body @{
        query_type = "POINT"
        entity = @{
            type = "CargoType"
            identifier = "煤炭及制品"
            resolution_strategy = "exact"
        }
        metric = "运输量"
        time_expression = "M202407"
        filters = @{
            route = "武汉-南京"
            ship_type = "散货船"
        }
    }

    # 3. 实体搜索业务场景
    Test-API -Category "BUSINESS" -Name "业务场景-货类实体搜索" -Method "POST" -Uri "$ApiBase/api/search/entities" -Body @{
        query = "煤炭"
        entity_type = "CargoType"
        strategy = "fuzzy"
        limit = 10
    }

    Test-API -Category "BUSINESS" -Name "业务场景-航线实体搜索" -Method "POST" -Uri "$ApiBase/api/search/entities" -Body @{
        query = "武汉"
        entity_type = "ShippingRoute"
        strategy = "fuzzy"
        limit = 15
    }

    Test-API -Category "BUSINESS" -Name "业务场景-综合实体搜索" -Method "POST" -Uri "$ApiBase/api/search/entities" -Body @{
        query = "长江"
        strategy = "fuzzy"
        limit = 20
    }

    # 4. 指标空值处理测试
    Test-API -Category "BUSINESS" -Name "业务场景-空值处理测试" -Method "POST" -Uri "$ApiBase/api/analytics/query" -Body @{
        query_type = "COMPARE"
        entity = @{
            type = "Port"
            identifier = @($PortName, "不存在的港口", $ComparePortName)
            resolution_strategy = "fuzzy"
        }
        metric = "总吞吐量"
        time_expression = "R6M"
    }

    Test-API -Category "BUSINESS" -Name "业务场景-指标不存在降级处理" -Method "POST" -Uri "$ApiBase/api/query" -Body @{
        query_type = "POINT"
        entity = @{
            type = "Ship"
            identifier = $ShipName
            resolution_strategy = "fuzzy"
        }
        metric = "不存在的指标"
        time_expression = "M202407"
    }

    # 5. 详细画像业务场景
    Test-API -Category "BUSINESS" -Name "业务场景-船舶完整画像" -Method "POST" -Uri "$ApiBase/api/query" -Body @{
        query_type = "PROFILE"
        entity = @{
            type = "Ship"
            identifier = $ShipName
            resolution_strategy = "fuzzy"
        }
        time_expression = "R6M"
    }

    # 注意：港口画像功能目前缺少港口货物数据，测试主要验证API结构
    Test-API -Category "BUSINESS" -Name "业务场景-港口运营画像" -Method "POST" -Uri "$ApiBase/api/query" -Body @{
        query_type = "PROFILE"
        entity = @{
            type = "Port"
            identifier = $PortName
            resolution_strategy = "fuzzy"
        }
        time_expression = "R12M"
    }

    # 6. 复杂对比分析业务场景
    Test-API -Category "BUSINESS" -Name "业务场景-多船舶运营效率对比" -Method "POST" -Uri "$ApiBase/api/analytics/query" -Body @{
        query_type = "COMPARE"
        entity = @{
            type = "Ship"
            identifier = @($ShipName, $CompareShipName, "长江001")
            resolution_strategy = "fuzzy"
        }
        metric = "有效营运率"
        time_expression = "R6M"
        filters = @{
            cargo_type = "煤炭及制品"
        }
    }

    Test-API -Category "BUSINESS" -Name "业务场景-航线运输能力对比" -Method "POST" -Uri "$ApiBase/api/analytics/query" -Body @{
        query_type = "COMPARE"
        entity = @{
            type = "ShippingRoute"
            identifier = @("武汉-南京", "武汉-上海", "重庆-武汉")
            resolution_strategy = "fuzzy"
        }
        metric = "航线货运量"
        time_expression = "R6M"
        filters = @{
            ship_type = "散货船"
        }
    }

    Test-API -Category "BUSINESS" -Name "业务场景-货类运输趋势对比" -Method "POST" -Uri "$ApiBase/api/analytics/query" -Body @{
        query_type = "COMPARE"
        entity = @{
            type = "CargoType"
            identifier = @("煤炭及制品", "金属矿石", "石油天然气及制品")
            resolution_strategy = "exact"
        }
        metric = "运输量"
        time_expression = "R12M"
    }

    # 7. 趋势分析业务场景
    Test-API -Category "BUSINESS" -Name "业务场景-船舶运营趋势分析" -Method "POST" -Uri "$ApiBase/api/query" -Body @{
        query_type = "TREND"
        entity = @{
            type = "Ship"
            identifier = $ShipName
            resolution_strategy = "fuzzy"
        }
        metric = "载货量"
        time_expression = "M202401_M202407"
    }

    Test-API -Category "BUSINESS" -Name "业务场景-港口吞吐量趋势" -Method "POST" -Uri "$ApiBase/api/query" -Body @{
        query_type = "TREND"
        entity = @{
            type = "Port"
            identifier = $PortName
            resolution_strategy = "fuzzy"
        }
        metric = "总吞吐量"
        time_expression = "Q2024Q1_Q2024Q2"
    }

    # 8. 排名分析业务场景
    Test-API -Category "BUSINESS" -Name "业务场景-船舶载货量排名" -Method "POST" -Uri "$ApiBase/api/analytics/query" -Body @{
        query_type = "RANK"
        entity = @{
            type = "Ship"
        }
        metric = "载货量"
        time_expression = "M202407"
        filters = @{
            cargo_type = "煤炭及制品"
        }
        # limit = 20
    }

    Test-API -Category "BUSINESS" -Name "业务场景-航线运量排名" -Method "POST" -Uri "$ApiBase/api/analytics/query" -Body @{
        query_type = "RANK"
        entity = @{
            type = "ShippingRoute"
        }
        metric = "航线货运量"
        time_expression = "R6M"
        filters = @{
            ship_type = "散货船"
        }
        # limit = 15
    }

    # 9. 综合业务场景 - 复杂查询组合
    Test-API -Category "BUSINESS" -Name "业务场景-多时间段对比分析" -Method "POST" -Uri "$ApiBase/api/analytics/query" -Body @{
        query_type = "COMPARE"
        entity = @{
            type = "Port"
            identifier = @($PortName, $ComparePortName)
            resolution_strategy = "fuzzy"
        }
        metric = "总吞吐量"
        time_expression = "Y2024"
        filters = @{
            cargo_type = @("煤炭及制品", "金属矿石")
        }
    }

    Test-API -Category "BUSINESS" -Name "业务场景-跨实体类型分析" -Method "POST" -Uri "$ApiBase/api/query" -Body @{
        query_type = "POINT"
        entity = @{
            type = "Province"
            identifier = "湖北省"
            resolution_strategy = "exact"
        }
        metric = "总货运量"
        time_expression = "M202407"
        filters = @{
            cargo_type = "煤炭及制品"
            ship_type = "散货船"
        }
    }

    # 10. 实时性业务场景
    Test-API -Category "BUSINESS" -Name "业务场景-最新数据查询" -Method "POST" -Uri "$ApiBase/api/query" -Body @{
        query_type = "POINT"
        entity = @{
            type = "Ship"
            identifier = $ShipName
            resolution_strategy = "fuzzy"
        }
        metric = "载货量"
        time_expression = "R1M"
    }

    # 11. 港口数据完整性检查
    Test-API -Category "BUSINESS" -Name "业务场景-港口货物数据检查" -Method "POST" -Uri "$ApiBase/api/query" -Body @{
        query_type = "POINT"
        entity = @{
            type = "Port"
            identifier = $PortName
            resolution_strategy = "fuzzy"
        }
        metric = "货物吞吐量"
        time_expression = "M202407"
    }

    # 12. 港口货物构成数据检查
    Test-API -Category "BUSINESS" -Name "业务场景-港口货物构成检查" -Method "POST" -Uri "$ApiBase/api/query" -Body @{
        query_type = "POINT"
        entity = @{
            type = "Port"
            identifier = $PortName
            resolution_strategy = "fuzzy"
        }
        metric = "货物构成"
        time_expression = "M202407"
    }

    Write-Info "业务场景测试完成 - 涵盖了组合指标、多维度查询、实体搜索、空值处理、画像分析、对比分析、趋势分析、排名分析、数据完整性检查等核心业务场景"
}

# 测试结果统计
Write-Host ""
Write-Host "========================================"
Write-Host "📊 测试结果统计"
Write-Host "========================================"

# 总体统计
$actualTotalTests = $global:BasicTests + $global:ProfileTests + $global:ComparisonTests + $global:AdvancedTests + $global:UtilityTests + $global:BusinessTests
$actualPassedTests = $global:BasicPassed + $global:ProfilePassed + $global:ComparisonPassed + $global:AdvancedPassed + $global:UtilityPassed + $global:BusinessPassed



$totalRate = if ($actualTotalTests -gt 0) { [math]::Round(($actualPassedTests * 100 / $actualTotalTests), 1) } else { 0 }
Write-Host "🎯 总体结果: $actualPassedTests/$actualTotalTests 通过 ($totalRate%)" -ForegroundColor $(if ($totalRate -ge 80) { "Green" } elseif ($totalRate -ge 60) { "Yellow" } else { "Red" })

# 分类统计
if ($global:BasicTests -gt 0) {
    $basicRate = [math]::Round(($global:BasicPassed * 100 / $global:BasicTests), 1)
    Write-Host "🔧 基础功能: $global:BasicPassed/$global:BasicTests 通过 ($basicRate%)" -ForegroundColor Blue
}

if ($global:ProfileTests -gt 0) {
    $profileRate = [math]::Round(($global:ProfilePassed * 100 / $global:ProfileTests), 1)
    Write-Host "👤 画像接口: $global:ProfilePassed/$global:ProfileTests 通过 ($profileRate%)" -ForegroundColor Blue
}

if ($global:ComparisonTests -gt 0) {
    $comparisonRate = [math]::Round(($global:ComparisonPassed * 100 / $global:ComparisonTests), 1)
    Write-Host "⚖️ 比较查询: $global:ComparisonPassed/$global:ComparisonTests 通过 ($comparisonRate%)" -ForegroundColor Blue
}

if ($global:AdvancedTests -gt 0) {
    $advancedRate = [math]::Round(($global:AdvancedPassed * 100 / $global:AdvancedTests), 1)
    Write-Host "🔬 高级分析: $global:AdvancedPassed/$global:AdvancedTests 通过 ($advancedRate%)" -ForegroundColor Blue
}

if ($global:UtilityTests -gt 0) {
    $utilityRate = [math]::Round(($global:UtilityPassed * 100 / $global:UtilityTests), 1)
    Write-Host "🔧 实用功能: $global:UtilityPassed/$global:UtilityTests 通过 ($utilityRate%)" -ForegroundColor Blue
}

if ($global:BusinessTests -gt 0) {
    $businessRate = [math]::Round(($global:BusinessPassed * 100 / $global:BusinessTests), 1)
    Write-Host "🏢 业务场景: $global:BusinessPassed/$global:BusinessTests 通过 ($businessRate%)" -ForegroundColor Blue
}

Write-Host ""
Write-Host "========================================"
Write-Host "✨ 测试完成！"
Write-Host "========================================"
