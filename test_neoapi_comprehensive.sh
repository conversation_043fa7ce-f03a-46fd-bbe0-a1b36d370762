#!/bin/bash

# NeoAPI 综合测试脚本
# 涵盖所有核心功能：基础API、画像接口、比较查询、空值处理、高级分析等
# 这是唯一的测试脚本，包含所有典型测试场景

set -e

# 配置参数
API_BASE="${API_BASE:-http://localhost:8080}"
SHIP_NAME="${SHIP_NAME:-汉海5号}"
SHIP_MMSI="${SHIP_MMSI:-413256960}"
PORT_NAME="${PORT_NAME:-武汉}"
COMPARE_SHIP_NAME="${COMPARE_SHIP_NAME:-汉海6号}"
COMPARE_PORT_NAME="${COMPARE_PORT_NAME:-张家港}"
THIRD_SHIP_NAME="${THIRD_SHIP_NAME:-汉海7号}"
THIRD_PORT_NAME="${THIRD_PORT_NAME:-万州}"

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 测试计数
TOTAL=0
PASSED=0

# 测试分类计数
BASIC_TESTS=0
BASIC_PASSED=0
PROFILE_TESTS=0
PROFILE_PASSED=0
COMPARISON_TESTS=0
COMPARISON_PASSED=0
ADVANCED_TESTS=0
ADVANCED_PASSED=0

# 显示帮助信息
show_help() {
    echo "NeoAPI 综合测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -b, --basic-only        仅运行基础功能测试"
    echo "  -p, --profile-only      仅运行画像接口测试"
    echo "  -c, --comparison-only   仅运行比较查询测试"
    echo "  -a, --advanced-only     仅运行高级分析测试"
    echo "  -q, --quick             快速测试（跳过耗时测试）"
    echo "  -v, --verbose           详细输出模式"
    echo ""
    echo "环境变量:"
    echo "  API_BASE               API服务地址 (默认: http://localhost:8080)"
    echo "  SHIP_NAME              测试船舶名称 (默认: 汉海5号)"
    echo "  PORT_NAME              测试港口名称 (默认: 重庆港)"
    echo "  COMPARE_SHIP_NAME      比较船舶名称 (默认: 汉海6号)"
    echo "  COMPARE_PORT_NAME      比较港口名称 (默认: 宜昌港)"
    echo ""
    echo "示例:"
    echo "  $0                     运行所有测试"
    echo "  $0 -b                  仅运行基础测试"
    echo "  $0 -q                  快速测试模式"
    echo "  API_BASE=http://prod:8080 $0  使用生产环境"
}

# 解析命令行参数
BASIC_ONLY=false
PROFILE_ONLY=false
COMPARISON_ONLY=false
ADVANCED_ONLY=false
QUICK_MODE=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -b|--basic-only)
            BASIC_ONLY=true
            shift
            ;;
        -p|--profile-only)
            PROFILE_ONLY=true
            shift
            ;;
        -c|--comparison-only)
            COMPARISON_ONLY=true
            shift
            ;;
        -a|--advanced-only)
            ADVANCED_ONLY=true
            shift
            ;;
        -q|--quick)
            QUICK_MODE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 -h 或 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 测试函数
test_api() {
    local category="$1"
    local name="$2"
    local cmd="$3"
    local expected_code="${4:-200}"
    
    TOTAL=$((TOTAL + 1))
    
    case $category in
        "BASIC")
            BASIC_TESTS=$((BASIC_TESTS + 1))
            ;;
        "PROFILE")
            PROFILE_TESTS=$((PROFILE_TESTS + 1))
            ;;
        "COMPARISON")
            COMPARISON_TESTS=$((COMPARISON_TESTS + 1))
            ;;
        "ADVANCED")
            ADVANCED_TESTS=$((ADVANCED_TESTS + 1))
            ;;
    esac
    
    echo -e "\n${BLUE}[$TOTAL]${NC} ${PURPLE}[$category]${NC} $name"
    
    # 执行命令并获取状态码
    if [ "$VERBOSE" = true ]; then
        echo -e "${CYAN}执行命令:${NC} $cmd"
    fi
    
    response=$(eval "$cmd" 2>/dev/null)
    status_code=$(echo "$response" | tail -n1 | grep -o '[0-9]\{3\}' | tail -n1)
    
    if [ "$status_code" = "$expected_code" ]; then
        echo -e "${GREEN}✅ 通过${NC} (状态码: $status_code)"
        PASSED=$((PASSED + 1))
        
        case $category in
            "BASIC")
                BASIC_PASSED=$((BASIC_PASSED + 1))
                ;;
            "PROFILE")
                PROFILE_PASSED=$((PROFILE_PASSED + 1))
                ;;
            "COMPARISON")
                COMPARISON_PASSED=$((COMPARISON_PASSED + 1))
                ;;
            "ADVANCED")
                ADVANCED_PASSED=$((ADVANCED_PASSED + 1))
                ;;
        esac
        
        # 显示关键信息
        if [ "$expected_code" = "200" ]; then
            echo "$response" | head -n-1 | jq -r '
                if .query_meta then
                    "   查询类型: " + (.query_meta.query_type // "N/A") +
                    if .query_meta.entities then " | 比较实体数: " + (.query_meta.entities | length | tostring) else "" end
                elif .data and .data.comparison then
                    "   比较结果: " + (.data.comparison | length | tostring) + "个实体" +
                    if .data.statistics then " | 数据可用率: " + ((.data.statistics.data_availability_rate * 100) | floor | tostring) + "%" else "" end
                elif .basic_info then
                    "   画像实体: " + (.basic_info.name // "N/A")
                elif .results then
                    "   搜索结果: " + (.total // 0 | tostring) + " 个"
                elif .status then
                    "   服务状态: " + (.status // "N/A")
                else
                    "   响应正常"
                end
            ' 2>/dev/null || echo "   响应正常"

            # 显示空值处理详情
            if [ "$VERBOSE" = true ]; then
                echo "$response" | head -n-1 | jq -r '
                    if .data and .data.comparison then
                        .data.comparison[] | "     - " + .entity_name + ": " + (.status // "unknown") +
                        if .message then " (" + .message + ")" else "" end +
                        if .value then " = " + (.value | tostring)
                        elif .profile_data and .profile_data.available_metrics then
                            " [完整属性: " + (.profile_data.available_metrics | keys | length | tostring) + "个指标]"
                        else "" end
                    else
                        empty
                    end
                ' 2>/dev/null || true
            fi
        fi
    else
        echo -e "${RED}❌ 失败${NC} (期望: $expected_code, 实际: $status_code)"
        if [ "$VERBOSE" = true ] && [ -n "$response" ]; then
            echo "$response" | head -n-1 | jq -r '.error // .message // "未知错误"' 2>/dev/null || echo "   无法解析错误信息"
        fi
    fi
}

# 显示测试开始信息
echo "========================================"
echo "🚀 NeoAPI 综合测试套件"
echo "========================================"
echo "API地址: $API_BASE"
echo "测试船舶: $SHIP_NAME ($SHIP_MMSI)"
echo "比较船舶: $COMPARE_SHIP_NAME, $THIRD_SHIP_NAME"
echo "测试港口: $PORT_NAME"
echo "比较港口: $COMPARE_PORT_NAME, $THIRD_PORT_NAME"
echo ""
echo "测试模式:"
if [ "$BASIC_ONLY" = true ]; then
    echo "  🔹 仅基础功能测试"
elif [ "$PROFILE_ONLY" = true ]; then
    echo "  🔹 仅画像接口测试"
elif [ "$COMPARISON_ONLY" = true ]; then
    echo "  🔹 仅比较查询测试"
elif [ "$ADVANCED_ONLY" = true ]; then
    echo "  🔹 仅高级分析测试"
else
    echo "  🔹 完整测试套件"
fi
if [ "$QUICK_MODE" = true ]; then
    echo "  ⚡ 快速模式"
fi
if [ "$VERBOSE" = true ]; then
    echo "  📝 详细输出"
fi
echo "========================================"

# 基础功能测试
if [ "$BASIC_ONLY" = true ] || [ "$PROFILE_ONLY" = false ] && [ "$COMPARISON_ONLY" = false ] && [ "$ADVANCED_ONLY" = false ]; then
    echo -e "\n${YELLOW}=== 🔧 基础功能测试 ===${NC}"
    
    test_api "BASIC" "API健康检查" \
    'curl -s -w "%{http_code}" "'"$API_BASE"'/api/health"'
    
    test_api "BASIC" "API欢迎页面" \
    'curl -s -w "%{http_code}" "'"$API_BASE"'/"'
    
    test_api "BASIC" "元数据-实体类型" \
    'curl -s -w "%{http_code}" "'"$API_BASE"'/api/metadata/entities"'
    
    test_api "BASIC" "元数据-指标列表" \
    'curl -s -w "%{http_code}" "'"$API_BASE"'/api/metadata/metrics"'
    
    test_api "BASIC" "搜索接口-GET方法" \
    'curl -s -w "%{http_code}" "'"$API_BASE"'/api/search/entities?q='"$SHIP_NAME"'&type=Ship&strategy=fuzzy&limit=5"'
    
    test_api "BASIC" "搜索接口-POST方法" \
    'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/search/entities" \
      -H "Content-Type: application/json" \
      -d "{\"query\":\"'"$SHIP_NAME"'\",\"entity_type\":\"Ship\",\"strategy\":\"fuzzy\",\"limit\":5}"'
    
    test_api "BASIC" "统一查询-POINT查询" \
    'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/query" \
      -H "Content-Type: application/json" \
      -d "{\"query_type\":\"POINT\",\"entity\":{\"type\":\"Ship\",\"identifier\":\"'"$SHIP_NAME"'\"},\"metric\":\"有效营运率\",\"time_expression\":\"M202407\"}"'
fi

# 画像接口测试
if [ "$PROFILE_ONLY" = true ] || [ "$BASIC_ONLY" = false ] && [ "$COMPARISON_ONLY" = false ] && [ "$ADVANCED_ONLY" = false ]; then
    echo -e "\n${YELLOW}=== 👤 画像接口测试 ===${NC}"
    
    test_api "PROFILE" "船舶画像-基本信息" \
    'curl -s -w "%{http_code}" "'"$API_BASE"'/api/profiles/ships/'"$SHIP_NAME"'?time_expression=R6M&detail_level=basic"'
    
    test_api "PROFILE" "船舶画像-详细信息" \
    'curl -s -w "%{http_code}" "'"$API_BASE"'/api/profiles/ships/'"$SHIP_NAME"'?time_expression=R6M&detail_level=full&include_history=true&include_cargo=true&include_analysis=true"'
    
    test_api "PROFILE" "港口画像-基本信息" \
    'curl -s -w "%{http_code}" "'"$API_BASE"'/api/profiles/ports/'"$PORT_NAME"'?time_expression=R6M&detail_level=basic"'
    
    test_api "PROFILE" "港口画像-详细信息" \
    'curl -s -w "%{http_code}" "'"$API_BASE"'/api/profiles/ports/'"$PORT_NAME"'?time_expression=R6M&detail_level=full&include_history=true&include_cargo=true&include_analysis=true"'
    
    if [ "$QUICK_MODE" = false ]; then
        test_api "PROFILE" "港口画像-年度数据" \
        'curl -s -w "%{http_code}" "'"$API_BASE"'/api/profiles/ports/'"$PORT_NAME"'?time_expression=R12M&detail_level=full&include_history=true"'
        
        test_api "PROFILE" "比较船舶画像" \
        'curl -s -w "%{http_code}" "'"$API_BASE"'/api/profiles/ships/'"$COMPARE_SHIP_NAME"'?time_expression=R6M&detail_level=full"'
    fi
fi

# 比较查询测试
if [ "$COMPARISON_ONLY" = true ] || [ "$BASIC_ONLY" = false ] && [ "$PROFILE_ONLY" = false ] && [ "$ADVANCED_ONLY" = false ]; then
    echo -e "\n${YELLOW}=== ⚖️ 比较查询测试 ===${NC}"

    test_api "COMPARISON" "核心查询-POINT查询" \
    'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/core/query" \
      -H "Content-Type: application/json" \
      -d "{\"query_type\":\"POINT\",\"entity\":{\"type\":\"Ship\",\"identifier\":\"'"$SHIP_NAME"'\"},\"metric\":\"有效营运率\",\"time_expression\":\"M202407\"}"'

    test_api "COMPARISON" "核心查询-PROFILE查询" \
    'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/core/query" \
      -H "Content-Type: application/json" \
      -d "{\"query_type\":\"PROFILE\",\"entity\":{\"type\":\"Ship\",\"identifier\":\"'"$SHIP_NAME"'\"},\"metric\":\"船舶画像\",\"time_expression\":\"R6M\"}"'

    test_api "COMPARISON" "船舶货运量比较" \
    'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/analytics/query" \
      -H "Content-Type: application/json" \
      -d "{\"query_type\":\"COMPARE\",\"entities\":[{\"type\":\"Ship\",\"identifier\":\"'"$SHIP_NAME"'\",\"resolution_strategy\":\"fuzzy\"},{\"type\":\"Ship\",\"identifier\":\"'"$COMPARE_SHIP_NAME"'\",\"resolution_strategy\":\"fuzzy\"}],\"metric\":\"货运量\",\"time_expression\":\"R6M\",\"options\":{\"include_history\":true,\"detail_level\":\"full\"}}"'

    test_api "COMPARISON" "船舶有效营运率比较" \
    'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/analytics/query" \
      -H "Content-Type: application/json" \
      -d "{\"query_type\":\"COMPARE\",\"entities\":[{\"type\":\"Ship\",\"identifier\":\"'"$SHIP_NAME"'\",\"resolution_strategy\":\"fuzzy\"},{\"type\":\"Ship\",\"identifier\":\"'"$COMPARE_SHIP_NAME"'\",\"resolution_strategy\":\"fuzzy\"}],\"metric\":\"有效营运率\",\"time_expression\":\"R6M\",\"options\":{\"comparison_type\":\"percentage_diff\",\"include_benchmark\":true}}"'

    test_api "COMPARISON" "港口吞吐量比较" \
    'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/analytics/query" \
      -H "Content-Type: application/json" \
      -d "{\"query_type\":\"COMPARE\",\"entities\":[{\"type\":\"Port\",\"identifier\":\"'"$PORT_NAME"'\",\"resolution_strategy\":\"fuzzy\"},{\"type\":\"Port\",\"identifier\":\"'"$COMPARE_PORT_NAME"'\",\"resolution_strategy\":\"fuzzy\"}],\"metric\":\"总吞吐量\",\"time_expression\":\"R6M\",\"options\":{\"comparison_type\":\"side_by_side\",\"include_trend\":true}}"'

    test_api "COMPARISON" "港口进港艘次比较" \
    'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/analytics/query" \
      -H "Content-Type: application/json" \
      -d "{\"query_type\":\"COMPARE\",\"entities\":[{\"type\":\"Port\",\"identifier\":\"'"$PORT_NAME"'\",\"resolution_strategy\":\"fuzzy\"},{\"type\":\"Port\",\"identifier\":\"'"$COMPARE_PORT_NAME"'\",\"resolution_strategy\":\"fuzzy\"}],\"metric\":\"进港艘次\",\"time_expression\":\"R6M\",\"options\":{\"comparison_type\":\"percentage_diff\",\"include_cargo_breakdown\":true}}"'

    # 空值处理专项测试
    test_api "COMPARISON" "空值处理-不存在港口" \
    'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/analytics/query" \
      -H "Content-Type: application/json" \
      -d "{\"query_type\":\"COMPARE\",\"entities\":[{\"type\":\"Port\",\"identifier\":\"'"$PORT_NAME"'\"},{\"type\":\"Port\",\"identifier\":\"不存在港口\"}],\"metric\":\"总吞吐量\",\"time_expression\":\"R6M\",\"options\":{\"comparison_type\":\"side_by_side\",\"include_statistics\":true}}"'

    test_api "COMPARISON" "空值处理-不存在指标" \
    'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/analytics/query" \
      -H "Content-Type: application/json" \
      -d "{\"query_type\":\"COMPARE\",\"entities\":[{\"type\":\"Port\",\"identifier\":\"'"$PORT_NAME"'\"}],\"metric\":\"不存在的指标\",\"time_expression\":\"R6M\",\"options\":{\"include_statistics\":true}}"'

    test_api "COMPARISON" "空值处理-船舶混合状态" \
    'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/analytics/query" \
      -H "Content-Type: application/json" \
      -d "{\"query_type\":\"COMPARE\",\"entities\":[{\"type\":\"Ship\",\"identifier\":\"'"$SHIP_NAME"'\"},{\"type\":\"Ship\",\"identifier\":\"不存在船舶\"}],\"metric\":\"货运量\",\"time_expression\":\"R6M\",\"options\":{\"comparison_type\":\"side_by_side\",\"include_statistics\":true}}"'

    if [ "$QUICK_MODE" = false ]; then
        test_api "COMPARISON" "三港口吞吐量比较" \
        'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/analytics/query" \
          -H "Content-Type: application/json" \
          -d "{\"query_type\":\"COMPARE\",\"entities\":[{\"type\":\"Port\",\"identifier\":\"'"$PORT_NAME"'\"},{\"type\":\"Port\",\"identifier\":\"'"$COMPARE_PORT_NAME"'\"},{\"type\":\"Port\",\"identifier\":\"'"$THIRD_PORT_NAME"'\"}],\"metric\":\"总吞吐量\",\"time_expression\":\"R6M\",\"options\":{\"comparison_type\":\"multi_entity\",\"include_ranking\":true}}"'

        test_api "COMPARISON" "三船舶货运量比较" \
        'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/analytics/query" \
          -H "Content-Type: application/json" \
          -d "{\"query_type\":\"COMPARE\",\"entities\":[{\"type\":\"Ship\",\"identifier\":\"'"$SHIP_NAME"'\"},{\"type\":\"Ship\",\"identifier\":\"'"$COMPARE_SHIP_NAME"'\"},{\"type\":\"Ship\",\"identifier\":\"'"$THIRD_SHIP_NAME"'\"}],\"metric\":\"货运量\",\"time_expression\":\"R6M\",\"options\":{\"comparison_type\":\"multi_entity\",\"include_ranking\":true}}"'

        test_api "COMPARISON" "大量实体混合比较" \
        'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/analytics/query" \
          -H "Content-Type: application/json" \
          -d "{\"query_type\":\"COMPARE\",\"entities\":[{\"type\":\"Port\",\"identifier\":\"'"$PORT_NAME"'\"},{\"type\":\"Port\",\"identifier\":\"'"$COMPARE_PORT_NAME"'\"},{\"type\":\"Port\",\"identifier\":\"万州港\"},{\"type\":\"Port\",\"identifier\":\"不存在港口1\"},{\"type\":\"Port\",\"identifier\":\"不存在港口2\"}],\"metric\":\"总吞吐量\",\"time_expression\":\"R6M\",\"options\":{\"comparison_type\":\"multi_entity\",\"include_ranking\":true,\"include_statistics\":true}}"'
    fi
fi

# 高级分析测试
if [ "$ADVANCED_ONLY" = true ] || [ "$BASIC_ONLY" = false ] && [ "$PROFILE_ONLY" = false ] && [ "$COMPARISON_ONLY" = false ]; then
    echo -e "\n${YELLOW}=== 🔬 高级分析测试 ===${NC}"

    test_api "ADVANCED" "港口排名查询" \
    'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/analytics/query" \
      -H "Content-Type: application/json" \
      -d "{\"query_type\":\"RANK\",\"entity_filter\":{\"type\":\"Port\",\"region\":\"长江流域\"},\"metric\":\"总吞吐量\",\"time_expression\":\"R6M\",\"options\":{\"rank_order\":\"desc\",\"limit\":10,\"include_percentage\":true}}"'

    test_api "ADVANCED" "船舶排名查询" \
    'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/analytics/query" \
      -H "Content-Type: application/json" \
      -d "{\"query_type\":\"RANK\",\"entity_filter\":{\"type\":\"Ship\",\"ship_type\":\"散货船\"},\"metric\":\"货运量\",\"time_expression\":\"R6M\",\"options\":{\"rank_order\":\"desc\",\"limit\":20,\"include_trend\":true}}"'

    test_api "ADVANCED" "船舶性能基准比较" \
    'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/analytics/query" \
      -H "Content-Type: application/json" \
      -d "{\"query_type\":\"COMPARE\",\"entities\":[{\"type\":\"Ship\",\"identifier\":\"'"$SHIP_NAME"'\"}],\"metric\":\"有效营运率\",\"time_expression\":\"R6M\",\"options\":{\"comparison_type\":\"benchmark\",\"benchmark_group\":\"同类型船舶\",\"include_percentile\":true,\"percentiles\":[25,50,75,90]}}"'

    test_api "ADVANCED" "港口船舶关联分析" \
    'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/analytics/query" \
      -H "Content-Type: application/json" \
      -d "{\"query_type\":\"COMPOSE\",\"primary_entity\":{\"type\":\"Port\",\"identifier\":\"'"$PORT_NAME"'\"},\"related_entities\":[{\"type\":\"Ship\",\"relationship\":\"frequent_visitor\",\"limit\":10}],\"metrics\":[\"总吞吐量\",\"进港艘次\"],\"time_expression\":\"R6M\",\"options\":{\"include_correlation\":true}}"'

    if [ "$QUICK_MODE" = false ]; then
        test_api "ADVANCED" "港口时间序列比较" \
        'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/analytics/query" \
          -H "Content-Type: application/json" \
          -d "{\"query_type\":\"COMPARE\",\"entities\":[{\"type\":\"Port\",\"identifier\":\"'"$PORT_NAME"'\"},{\"type\":\"Port\",\"identifier\":\"'"$COMPARE_PORT_NAME"'\"}],\"metric\":\"总吞吐量\",\"time_expression\":\"R12M\",\"options\":{\"comparison_type\":\"time_series\",\"granularity\":\"monthly\",\"include_forecast\":true}}"'

        test_api "ADVANCED" "船舶航线组合分析" \
        'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/analytics/query" \
          -H "Content-Type: application/json" \
          -d "{\"query_type\":\"COMPOSE\",\"primary_entity\":{\"type\":\"Ship\",\"identifier\":\"'"$SHIP_NAME"'\"},\"related_entities\":[{\"type\":\"ShippingRoute\",\"relationship\":\"frequently_used\",\"limit\":5}],\"metrics\":[\"货运量\",\"航次数\"],\"time_expression\":\"R6M\",\"options\":{\"include_efficiency_analysis\":true}}"'

        test_api "ADVANCED" "实时数据接口" \
        'curl -s -w "%{http_code}" "'"$API_BASE"'/api/realtime/Ship/'"$SHIP_NAME"'"' \
        404
    fi
fi

# 错误处理测试
if [ "$BASIC_ONLY" = false ] && [ "$PROFILE_ONLY" = false ] && [ "$COMPARISON_ONLY" = false ] && [ "$ADVANCED_ONLY" = false ]; then
    echo -e "\n${YELLOW}=== 🚫 错误处理测试 ===${NC}"

    test_api "BASIC" "错误处理-核心查询无效类型" \
    'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/core/query" \
      -H "Content-Type: application/json" \
      -d "{\"query_type\":\"COMPARE\",\"entity\":{\"type\":\"Ship\",\"identifier\":\"'"$SHIP_NAME"'\"},\"metric\":\"货运量\",\"time_expression\":\"R6M\"}"' \
    400

    test_api "BASIC" "错误处理-分析查询无效类型" \
    'curl -s -w "%{http_code}" -X POST "'"$API_BASE"'/api/analytics/query" \
      -H "Content-Type: application/json" \
      -d "{\"query_type\":\"POINT\",\"entity\":{\"type\":\"Ship\",\"identifier\":\"'"$SHIP_NAME"'\"},\"metric\":\"货运量\",\"time_expression\":\"R6M\"}"' \
    400

    test_api "BASIC" "错误处理-无效元数据类型" \
    'curl -s -w "%{http_code}" "'"$API_BASE"'/api/metadata/invalid_type"' \
    400

    test_api "BASIC" "错误处理-缺少搜索参数" \
    'curl -s -w "%{http_code}" "'"$API_BASE"'/api/search/entities"' \
    400
fi

# 显示测试结果统计
echo ""
echo "========================================"
echo "📊 测试结果统计"
echo "========================================"

# 分类统计
if [ $BASIC_TESTS -gt 0 ]; then
    basic_rate=$(echo "scale=1; $BASIC_PASSED * 100 / $BASIC_TESTS" | bc 2>/dev/null || echo "N/A")
    echo -e "${BLUE}🔧 基础功能:${NC} $BASIC_PASSED/$BASIC_TESTS 通过 (${basic_rate}%)"
fi

if [ $PROFILE_TESTS -gt 0 ]; then
    profile_rate=$(echo "scale=1; $PROFILE_PASSED * 100 / $PROFILE_TESTS" | bc 2>/dev/null || echo "N/A")
    echo -e "${PURPLE}👤 画像接口:${NC} $PROFILE_PASSED/$PROFILE_TESTS 通过 (${profile_rate}%)"
fi

if [ $COMPARISON_TESTS -gt 0 ]; then
    comparison_rate=$(echo "scale=1; $COMPARISON_PASSED * 100 / $COMPARISON_TESTS" | bc 2>/dev/null || echo "N/A")
    echo -e "${CYAN}⚖️ 比较查询:${NC} $COMPARISON_PASSED/$COMPARISON_TESTS 通过 (${comparison_rate}%)"
fi

if [ $ADVANCED_TESTS -gt 0 ]; then
    advanced_rate=$(echo "scale=1; $ADVANCED_PASSED * 100 / $ADVANCED_TESTS" | bc 2>/dev/null || echo "N/A")
    echo -e "${YELLOW}🔬 高级分析:${NC} $ADVANCED_PASSED/$ADVANCED_TESTS 通过 (${advanced_rate}%)"
fi

echo ""
echo "总体统计:"
echo "  总测试数: $TOTAL"
echo "  通过数: $PASSED"
echo "  失败数: $((TOTAL - PASSED))"

if [ $TOTAL -gt 0 ]; then
    success_rate=$(echo "scale=1; $PASSED * 100 / $TOTAL" | bc 2>/dev/null || echo "N/A")
    echo "  成功率: ${success_rate}%"
fi

echo ""
if [ $PASSED -eq $TOTAL ]; then
    echo -e "${GREEN}🎉 所有测试通过！${NC}"
    echo "NeoAPI统一架构功能正常"
    exit_code=0
else
    echo -e "${RED}⚠️ 有 $((TOTAL - PASSED)) 个测试失败${NC}"
    if [ $TOTAL -gt 0 ]; then
        echo "建议检查失败的测试用例"
    fi
    exit_code=1
fi

# 显示测试覆盖范围
echo ""
echo "🔍 测试覆盖范围："
echo "  ✓ API基础功能（健康检查、元数据、搜索）"
echo "  ✓ 画像接口（船舶画像、港口画像）"
echo "  ✓ 比较查询（实体对比、多实体比较）"
echo "  ✓ 空值处理（智能降级、完整属性返回）"
echo "  ✓ 高级分析（排名、基准、关联分析）"
echo "  ✓ 错误处理（参数验证、类型检查）"
echo ""
echo "🎯 空值处理策略："
echo "  ✓ success: 成功获取特定指标"
echo "  ✓ metric_unavailable_full_profile_returned: 指标不可用，返回完整属性"
echo "  ✓ zero_value: 指标值为零"
echo "  ✓ error: 完全无法获取数据"
echo ""
echo "📊 统计信息说明："
echo "  ✓ specific_metric_availability_rate: 特定指标可用率"
echo "  ✓ data_availability_rate: 总体数据可用率（含完整属性）"
echo "  ✓ full_profile_count: 返回完整属性的实体数量"

echo ""
echo "📝 测试完成时间: $(date)"
echo "========================================"

exit $exit_code
