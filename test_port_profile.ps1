# Test port profile with route connections
param([string]$ApiBase = "http://localhost:8080")

Write-Host "Testing Port Profile with Route Connections" -ForegroundColor Yellow

try {
    $body = @{
        query_type = "PROFILE"
        entity = @{
            type = "Port"
            identifier = "武汉"
            resolution_strategy = "fuzzy"
        }
        time_expression = "M202407"
    } | ConvertTo-Json -Depth 10

    Write-Host "Request:" -ForegroundColor Gray
    Write-Host $body -ForegroundColor White
    
    Write-Host "Sending port profile request..." -ForegroundColor Gray
    $response = Invoke-RestMethod -Uri "$ApiBase/api/query" -Method POST -Body $body -ContentType "application/json"
    
    Write-Host "Response:" -ForegroundColor Green
    Write-Host "Status: $($response.status)" -ForegroundColor White
    Write-Host "Entity: $($response.entity)" -ForegroundColor White
    Write-Host "Metric: $($response.metric)" -ForegroundColor White
    Write-Host "Period: $($response.period)" -ForegroundColor White
    
    if ($response.status -eq "success" -and $response.data) {
        Write-Host "Data structure analysis:" -ForegroundColor Cyan
        
        # Check basic_info
        if ($response.data.basic_info) {
            Write-Host "  ✅ basic_info: Present" -ForegroundColor Green
        } else {
            Write-Host "  ❌ basic_info: Missing" -ForegroundColor Red
        }
        
        # Check recent_stats
        if ($response.data.recent_stats) {
            Write-Host "  ✅ recent_stats: Present" -ForegroundColor Green
        } else {
            Write-Host "  ❌ recent_stats: Missing" -ForegroundColor Red
        }
        
        # Check historical_performance
        if ($response.data.historical_performance) {
            $count = if ($response.data.historical_performance.Count) { $response.data.historical_performance.Count } else { "unknown" }
            Write-Host "  ✅ historical_performance: Present (count: $count)" -ForegroundColor Green
        } else {
            Write-Host "  ❌ historical_performance: Missing" -ForegroundColor Red
        }
        
        # Check cargo_breakdown
        if ($response.data.cargo_breakdown) {
            $count = if ($response.data.cargo_breakdown.Count) { $response.data.cargo_breakdown.Count } else { "unknown" }
            Write-Host "  ✅ cargo_breakdown: Present (count: $count)" -ForegroundColor Green
        } else {
            Write-Host "  ❌ cargo_breakdown: Missing" -ForegroundColor Red
        }
        
        # Check route_connections (NEW)
        if ($response.data.route_connections) {
            $count = if ($response.data.route_connections.Count) { $response.data.route_connections.Count } else { "unknown" }
            Write-Host "  ✅ route_connections: Present (count: $count)" -ForegroundColor Green
            
            # Show route connections details
            if ($response.data.route_connections.Count -gt 0) {
                Write-Host "  Route connections details:" -ForegroundColor Yellow
                $response.data.route_connections | ForEach-Object {
                    Write-Host "    - $($_.destination): ships=$($_.ship_count), cargo=$($_.cargo_volume)" -ForegroundColor Gray
                }
            }
        } else {
            Write-Host "  ❌ route_connections: Missing" -ForegroundColor Red
        }
        
        # Check summary
        if ($response.data.summary) {
            Write-Host "  ✅ summary: Present" -ForegroundColor Green
        } else {
            Write-Host "  ❌ summary: Missing" -ForegroundColor Red
        }
        
    } else {
        Write-Host "ERROR: Query failed or no data returned" -ForegroundColor Red
        if ($response.error) {
            Write-Host "Error message: $($response.error)" -ForegroundColor Red
        }
    }
    
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "HTTP Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}
