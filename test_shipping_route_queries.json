{"test_suite": "航线查询测试用例", "description": "涵盖各种航线查询场景的测试用例集合", "base_url": "http://localhost:8080/api/v3.1/query/entities", "test_cases": [{"test_id": "ROUTE_001", "name": "基础航线名称搜索", "description": "搜索名称包含'武汉'的航线", "category": "基础搜索", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "filters": {"name": "武汉"}, "limit": 20}, "expected_conditions": {"total_found": ">= 1", "results[*].name": "contains '武汉'", "results[*].metrics.total_cargo": "> 0", "results": "ordered by total_cargo desc"}}, {"test_id": "ROUTE_002", "name": "起点港口过滤", "description": "查找从武汉出发的所有航线", "category": "地理位置过滤", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "filters": {"origin_ports": ["武汉"]}, "limit": 20}, "expected_conditions": {"results[*].properties.origin_port": "== '武汉'", "results[*].ports": "contains '武汉'"}}, {"test_id": "ROUTE_003", "name": "终点港口过滤", "description": "查找到达上海的航线", "category": "地理位置过滤", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "filters": {"destination_ports": ["上海"]}, "limit": 20}, "expected_conditions": {"results[*].properties.destination_port": "== '上海'", "results[*].ports": "contains '上海'"}}, {"test_id": "ROUTE_004", "name": "起终点组合过滤", "description": "查找从武汉到南京的航线", "category": "地理位置过滤", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "filters": {"origin_ports": ["武汉"], "destination_ports": ["南京"]}, "limit": 10}, "expected_conditions": {"results[*].properties.origin_port": "== '武汉'", "results[*].properties.destination_port": "== '南京'"}}, {"test_id": "ROUTE_005", "name": "航线类型过滤", "description": "查找所有干线航线", "category": "分类属性过滤", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "filters": {"route_types": ["干线"]}, "limit": 30}, "expected_conditions": {"results[*].properties.route_type": "== '干线'"}}, {"test_id": "ROUTE_006", "name": "多航线类型过滤", "description": "查找干线和支线航线", "category": "分类属性过滤", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "filters": {"route_types": ["干线", "支线"]}, "limit": 50}, "expected_conditions": {"results[*].properties.route_type": "in ['干线', '支线']"}}, {"test_id": "ROUTE_007", "name": "距离范围过滤", "description": "查找距离在200-500公里之间的航线", "category": "数值范围过滤", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "filters": {"distance_min": 200, "distance_max": 500}, "limit": 25}, "expected_conditions": {"results[*].properties.distance_km": "between 200 and 500"}}, {"test_id": "ROUTE_008", "name": "长距离航线查询", "description": "查找距离超过1000公里的长距离航线", "category": "数值范围过滤", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "filters": {"distance_min": 1000}, "limit": 15}, "expected_conditions": {"results[*].properties.distance_km": ">= 1000"}}, {"test_id": "ROUTE_009", "name": "短距离航线查询", "description": "查找距离小于100公里的短距离航线", "category": "数值范围过滤", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "filters": {"distance_max": 100}, "limit": 20}, "expected_conditions": {"results[*].properties.distance_km": "<= 100"}}, {"test_id": "ROUTE_010", "name": "高运量航线查询", "description": "查找货运量超过100万吨的航线", "category": "性能阈值过滤", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "filters": {"min_cargo_volume": 1000000}, "limit": 10}, "expected_conditions": {"results[*].metrics.total_cargo": ">= 1000000"}}, {"test_id": "ROUTE_011", "name": "高船舶数航线查询", "description": "查找船舶数量超过50艘的繁忙航线", "category": "性能阈值过滤", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "filters": {"min_ships": 50}, "limit": 15}, "expected_conditions": {"results[*].metrics.ship_count": ">= 50"}}, {"test_id": "ROUTE_012", "name": "高航次数航线查询", "description": "查找航次数超过200次的活跃航线", "category": "性能阈值过滤", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "filters": {"min_voyages": 200}, "limit": 12}, "expected_conditions": {"results[*].metrics.voyage_count": ">= 200"}}, {"test_id": "ROUTE_013", "name": "复合条件查询1", "description": "长江干线航线：从上游到下游，距离超过800公里，船舶数量超过20艘", "category": "复合条件查询", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "filters": {"origin_ports": ["重庆", "宜昌", "武汉"], "destination_ports": ["南京", "上海", "苏州"], "route_types": ["干线"], "distance_min": 800, "min_ships": 20}, "limit": 10}, "expected_conditions": {"results[*].properties.route_type": "== '干线'", "results[*].properties.distance_km": ">= 800", "results[*].metrics.ship_count": ">= 20"}}, {"test_id": "ROUTE_014", "name": "复合条件查询2", "description": "中等距离的支线航线，货运量适中", "category": "复合条件查询", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "filters": {"route_types": ["支线"], "distance_min": 100, "distance_max": 400, "min_cargo_volume": 50000, "max_cargo_volume": 500000}, "limit": 20}, "expected_conditions": {"results[*].properties.route_type": "== '支线'", "results[*].properties.distance_km": "between 100 and 400", "results[*].metrics.total_cargo": "between 50000 and 500000"}}, {"test_id": "ROUTE_015", "name": "长三角区域航线", "description": "长三角地区内的航线网络", "category": "区域查询", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "filters": {"origin_ports": ["上海", "南京", "苏州", "无锡", "常州"], "destination_ports": ["上海", "南京", "苏州", "无锡", "常州"]}, "limit": 30}, "expected_conditions": {"results[*].properties.origin_port": "in ['上海', '南京', '苏州', '无锡', '常州']", "results[*].properties.destination_port": "in ['上海', '南京', '苏州', '无锡', '常州']"}}, {"test_id": "ROUTE_016", "name": "排除特定航线", "description": "除了武汉-上海航线外的其他长距离航线", "category": "排除条件查询", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "filters": {"distance_min": 500, "exclude_routes": ["武汉-上海航线"]}, "limit": 15}, "expected_conditions": {"results[*].properties.distance_km": ">= 500", "results[*].name": "not contains '武汉-上海航线'"}}, {"test_id": "ROUTE_017", "name": "纯排名查询", "description": "货运量最大的TOP20航线", "category": "排名查询", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "limit": 20}, "expected_conditions": {"total_found": "> 0", "returned": "<= 20", "results": "ordered by total_cargo desc"}}, {"test_id": "ROUTE_018", "name": "小规模航线查询", "description": "船舶数量较少的小规模航线", "category": "性能阈值过滤", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "filters": {"max_ships": 10, "max_cargo_volume": 100000}, "limit": 25}, "expected_conditions": {"results[*].metrics.ship_count": "<= 10", "results[*].metrics.total_cargo": "<= 100000"}}, {"test_id": "ROUTE_019", "name": "特定航线代码查询", "description": "通过航线代码查找航线", "category": "精确匹配查询", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "filters": {"route_codes": ["WH-NJ", "WH-SH", "CQ-WH"]}, "limit": 10}, "expected_conditions": {"results[*].properties.route_id": "in ['WH-NJ', 'WH-SH', 'CQ-WH']"}}, {"test_id": "ROUTE_020", "name": "空过滤条件查询", "description": "无任何过滤条件的查询，返回按货运量排序的航线", "category": "边界条件测试", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "limit": 50}, "expected_conditions": {"total_found": "> 0", "returned": "<= 50", "results": "ordered by total_cargo desc"}}], "edge_case_tests": [{"test_id": "ROUTE_EDGE_001", "name": "无结果查询", "description": "过滤条件过严导致无结果", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "filters": {"distance_min": 10000, "min_cargo_volume": 999999999}, "limit": 10}, "expected_conditions": {"total_found": "== 0", "returned": "== 0", "results": "== []"}}, {"test_id": "ROUTE_EDGE_002", "name": "参数验证错误", "description": "距离范围参数错误", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "filters": {"distance_min": 1000, "distance_max": 500}, "limit": 10}, "expected_response": {"status": "error", "error": "Invalid parameter", "message": "distance_min cannot be greater than distance_max"}}, {"test_id": "ROUTE_EDGE_003", "name": "大数据量查询", "description": "请求大量结果的性能测试", "request": {"entity_type": "ShippingRoute", "time_expression": "M202506", "limit": 100}, "performance_expectations": {"execution_time_ms": "< 1000", "memory_usage": "< 100MB"}}], "curl_commands": [{"test_id": "ROUTE_001", "command": "curl -X POST http://localhost:8080/api/v3.1/query/entities -H \"Content-Type: application/json\" -d '{\"entity_type\":\"ShippingRoute\",\"time_expression\":\"M202506\",\"filters\":{\"name\":\"武汉\"},\"limit\":20}'"}, {"test_id": "ROUTE_013", "command": "curl -X POST http://localhost:8080/api/v3.1/query/entities -H \"Content-Type: application/json\" -d '{\"entity_type\":\"ShippingRoute\",\"time_expression\":\"M202506\",\"filters\":{\"origin_ports\":[\"重庆\",\"宜昌\",\"武汉\"],\"destination_ports\":[\"南京\",\"上海\",\"苏州\"],\"route_types\":[\"干线\"],\"distance_min\":800,\"min_ships\":20},\"limit\":10}'"}, {"test_id": "ROUTE_017", "command": "curl -X POST http://localhost:8080/api/v3.1/query/entities -H \"Content-Type: application/json\" -d '{\"entity_type\":\"ShippingRoute\",\"time_expression\":\"M202506\",\"limit\":20}'"}]}